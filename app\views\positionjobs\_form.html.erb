<style>
    .is-invalid .tox-tinymce {
        border-color: #dc3545;
    }
    
    .invalid-feedback {
        display: block;
        color: #dc3545;
        margin-top: 0.25rem;
    }
</style>

<%= form_for @positionjob,
                         method: (@positionjob.new_record? ? :post : :patch),
                         remote: true do |form| %>
    <div class="modal-body">
        <div class="form-group mb-3">
            <%= form.label :name, 'Tên độ cấp quyền hạn', class: 'form-label' %>
            <%= form.text_field :name, class: "form-control", id: 'tf_name', placeholder: 'Nhập tên độ cấp quyền hạn' %>
            <% if @positionjob.errors[:name].any? %>
              <div class="invalid-feedback">
                <%= @positionjob.errors[:name].join(", ") %>
              </div>
            <% end %>
        </div>

        <div class="form-group mb-3">
            <%= form.label :scode, 'Mã', class: 'form-label' %>
            <%= form.text_field :scode, class: "form-control", id: 'tf_scode', placeholder: 'Nhập mã' %>
        </div>

        <div class="form-group mb-3">
            <%= form.label :iorder, 'Quyền hạn', class: 'form-label' %>
            <%= form.select :iorder, 
                            options_for_select((1..10), @positionjob.iorder || ''),
                            { include_blank: 'Chọn quyền hạn' }, 
                            { class: "form-select", id: 'function_select' } %>
        </div>

        <div class="form-group mb-3">
            <%= form.label :note, 'Ghi chú', class: 'form-label' %>
            <%= form.text_area :note, rows: 3, class: "form-control", placeholder: 'Nhập ghi chú' %>
        </div>
    </div>

    <div class="modal-footer border-top-0">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <%= form.submit 'Lưu', class: 'btn btn-primary' %>
    </div>
<% end %>

<script>
    function getTextToASCII() {
        var value_name = document.getElementById("tf_name").value;
        var value_scode = document.getElementById("tf_scode");
        if (value_name) {
        var content = removeVietnameseTones(value_name).replace(/ /g, '-');
            if (value_scode) {
                        value_scode.value = content.toUpperCase()
                    }
        }
      }
    function removeVietnameseTones(str) {
        str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g,"a"); 
        str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g,"e"); 
        str = str.replace(/ì|í|ị|ỉ|ĩ/g,"i"); 
        str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g,"o"); 
        str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g,"u"); 
        str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g,"y"); 
        str = str.replace(/đ/g,"d");
        str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
        str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
        str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
        str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
        str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
        str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
        str = str.replace(/Đ/g, "D");
        // Some system encode vietnamese combining accent as individual utf-8 characters
        // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
        str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
        str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
        // Remove extra spaces
        // Bỏ các khoảng trắng liền nhau
        str = str.replace(/ + /g," ");
        str = str.trim();
        // Remove punctuations
        // Bỏ dấu câu, kí tự đặc biệt
        str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g," ");
        return str;
    }

    document.getElementById("tf_name").addEventListener("keyup", function() {getTextToASCII()} );
</script>