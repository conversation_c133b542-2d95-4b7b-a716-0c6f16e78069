<% content_for :head do %>
  <%= stylesheet_link_tag "select2-bootstrap-5-theme.min" %>
  <%= stylesheet_link_tag "select2.min" %>
<% end %>
<% content_for :bottombody do %>
    <%= javascript_include_tag 'select2.full.min'%>
    <%= javascript_include_tag 'select2.min.js'%>
<% end %> 

<style>
    #table_attend_teacher_wrapper .top {
        display: none !important;
    }
</style>
<div class="mt-2">
    <div class="row">
        <div class="col-md-6">
            <div class="col-md-6">
                <%= form_tag works_index_path(tab: @tab_names[:dueties], lang: session[:lang]), method: :post do %>
                    <div class="input-group">
                        <%= text_field_tag :search, params[:search], class: "form-control", id: "duety_search", placeholder: "T<PERSON>m kiếm" %>
                        <span class="input-group-text bg-white">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-search" viewBox="0 0 16 16">
                            <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.*************.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                        </svg>
                        </span>
                    </div>
                <% end %>
            </div>
        </div>
        <div class="col-md-6 d-flex justify-content-end">
            <%= link_to new_duety_path, class: "btn btn-primary", id: "btn-add-function", onclick: "showLoadding(true);", remote: true do %>
                <i class="fas fa-plus"></i> Thêm nhiệm vụ
            <% end %>
        </div>
    </div>
</div>
<table id="table_dueties" class="table table-customs table-bordered fs--1 mb-0" style="margin-top: 15px !important; margin-bottom: 0px !important">
    <thead>
        <tr style="background-color: #EFF2F1;">
            <th class="no-sort" style="width: 5%;">#</th>
            <th class="no-sort" style="width: 30%;">Tên nhiệm vụ</th>
            <th class="no-sort" style="width: 30%;">Thuộc chức năng</th>
            <th class="no-sort" style="width: 25%;">Mô tả</th>
            <th class="no-sort" style="width: 10%;">Hành động</th>
        </tr>
    </thead>
    <tbody class="list">
        <% if @dueties.any? %>
            <% @dueties.each_with_index do |dt, index| %>
                <tr id="duety-<%= dt['id'] %>">
                    <td><%= (session[:page].to_i - 1) * session[:per_page].to_i + index + 1 %></td>
                    <td class="text-start"><%= dt.name %></td>
                    <td class="text-start"><%= dt.parent_name %></td>
                    <td class="text-start"><%= dt.sdesc %></td>
                    <td class="text-center">
                        <div class="d-flex justify-content-center align-items-center">
                            <%= link_to edit_duety_path(dt), class: 'btn btn-link', onclick: "showLoadding(true);",
                                        data: { bs_toggle: 'tooltip', bs_placement: 'top', bs_title: 'Chỉnh sửa' },
                                        remote: true do %>
                                <i class="text-primary fas fa-pen"></i>
                            <% end %>
                            
                            <%= link_to duety_path(dt), class: 'btn btn-link delete-duety-btn', 
                                        method: :delete,
                                        data: { 
                                            bs_toggle: 'tooltip', 
                                            bs_placement: 'top', 
                                            bs_title: 'Xóa',
                                            can_delete: dt.can_be_deleted,
                                            duety_name: dt.name,
                                            duety_id: dt.id,
                                            remote: true
                                        } do %>
                            <i class="text-danger fas fa-trash"></i>
                            <% end %>
                        </div>
                    </td>
                </tr>
            <% end %>
        <% else %>
            <tr>
                <td colspan="5" class="text-center">Không có dữ liệu</td>
            </tr>
        <% end %>
    </tbody>
</table>

<div class='d-flex justify-content-between align-items-center mt-3'>
    <div style="font-size: 14px; font-weight: 400; color: #4E606E;">
        <%
            current_page = session[:page]&.to_i || 1
            per_page = session[:per_page]&.to_i || 10
            start_record = (current_page - 1) * per_page + 1
            end_record = [current_page * per_page, @total_records].min
        %>

        <%=lib_translate('Show')%> <%= start_record %>-<%= end_record %> <%=lib_translate('Up_to')%> <%= @total_records %> <%=lib_translate('Record')%>
    </div>
    <div>
        <%= render_pagination_limit_offset(works_index_path(tab: @tab_names[:dueties], lang: session[:lang], per_page: session[:per_page], search: session[:search]), 10, @dueties.count).html_safe %>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        document.querySelectorAll('.delete-duety-btn').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const canDelete = this.getAttribute('data-can-delete') === 'true';
                const duetyName = this.getAttribute('data-duety-name');
                
                if (canDelete) {
                    if (confirm(`Xác nhận xóa nhiệm vụ ${duetyName}?`)) {
                        showLoadding(true);

                        const token = $('meta[name="csrf-token"]').attr('content');
                        
                        $.ajax({
                            url: this.getAttribute('href'),
                            type: 'DELETE',
                            dataType: 'script',
                            headers: {
                                'X-CSRF-Token': token
                            },
                            success: function(response) {
                            },
                            error: function(xhr) {
                                showLoadding(false);
                                alert('Có lỗi xảy ra khi xóa nhiệm vụ');
                            }
                        });
                    }
                } else {
                    alert("Đối với các nhiệm vụ đã được gán công việc, hệ thống sẽ không cho phép xóa. Để xóa nhiệm vụ, vui lòng xóa toàn bộ các công việc liên quan trước.");
                }
                
                return false;
            });
        });
    });
</script>
