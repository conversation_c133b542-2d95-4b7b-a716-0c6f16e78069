<nav class="navbar navbar-light navbar-vertical navbar-expand-xl">
    <div class="d-flex align-items-center">
      <div class="toggle-icon-wrapper">
        <button class="btn navbar-toggler-humburger-icon navbar-vertical-toggle"><span class="navbar-toggle-icon"><span class="toggle-line"></span></span></button>
      </div>
      <a class="navbar-brand" href="<%= root_path(lang: session[:lang]) %>">
        <div class="d-flex align-items-center py-3">
          <% if @user_info.present? && @user_info[:organizations]&.any? { |scode| %w[BMU BMTU].include?(scode) } %>
            <img class="me-2 img_logo_bmtu" src="<%= image_path("/assets/image/logo.svg")  %>"  width="150"/>
          <% elsif @user_info.present? && @user_info[:organizations]&.any? { |scode| %w[BUH].include?(scode) } %>
            <%= image_tag "buhlogo.png", class: "me-2", width: "120" %>
          <% else %>
            <img class="me-2 img_logo_bmtu" src="<%= image_path("/assets/image/logo.svg")  %>"  width="150"/>
          <% end %>
        </div>
      </a>
    </div>
    <div class="collapse navbar-collapse" id="navbarVerticalCollapse">
      <div id="left_menu_application" class="navbar-vertical-content scrollbar scrollbar_menuleft">
        <ul class="navbar-nav flex-column mb-3">
          <!-- Individual -->
          <% if is_access(session["user_id"], "DASHBOARD","READ") %>
            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label"><%= lib_translate("Individual")%></div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>
              <!-- Dashboard -->
              <a class="nav-link <%= 'active' if '/' == request.env['PATH_INFO'] %>" id="dashboard"  href="<%= root_path(lang: session[:lang]) %>" role="button">
                <div class="d-flex align-items-center">
                  <span class="nav-link-icon">
                    <span class="ierp i_dashboard"></span>
                  </span>
                  <span class="nav-link-text ps-1"><%= lib_translate("dashboard") %></span>
                </div>
              </a>
              <!-- Notifies -->
              <a class="nav-link <%= 'active' if '/notifies/index' == request.env['PATH_INFO'] %>" href="<%= notifies_index_path(lang: session[:lang]) %>">
                <div class="d-flex align-items-center">
                  <span class="nav-link-icon"><span class="ierp ithong_bao" ></span></span>
                  <span class="nav-link-text ps-1"> <%= lib_translate("Notifies") %> </span>
                </div>
              </a>
          <%end%>

          <!-- Document Management -->
          <li class="nav-item">
            <!-- Lable Personnel -->
            <% if is_access(session["user_id"], "MANDOCS-INCOMING","READ") || is_access(session["user_id"], "MANDOCS-OUTGOING","READ") || is_access(session["user_id"], "MANDOCS-SEARCH","READ") || is_access(session["user_id"], "MANDOCS-PROCESS","READ") || is_access(session["user_id"], "MANDOCBOOK","READ") || is_access(session["user_id"], "MANDOCTYPE","READ") || is_access(session["user_id"], "MANDOCPRIORITY","READ") || is_access(session["user_id"], "MANDOCFROMS","READ") || is_access(session["user_id"], "FORMS","READ") || is_access(session["user_id"], "SFTRAINING","READ") || is_access(session["user_id"], "ASSETS","READ") %>
              <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
                <div class="col-auto navbar-vertical-label"><%= lib_translate("System") %></div>
                <div class="col ps-0">
                  <hr class="mb-0 navbar-vertical-divider" />
                </div>
              </div>
            <% end %>
            <%# Quản lý nhân sự %>
            <%= render "layouts/shared/organization_menu"%>

            <%#  ================================================ Quản lý văn bản  ================================================%>
            <% if is_access(session["user_id"], "MANDOCS-INCOMING","READ") || is_access(session["user_id"], "MANDOCS-OUTGOING","READ") || is_access(session["user_id"], "MANDOCS-SEARCH","READ") || is_access(session["user_id"], "MANDOCS-PROCESS","READ") || is_access(session["user_id"], "MANDOCBOOK","READ") || is_access(session["user_id"], "MANDOCTYPE","READ") || is_access(session["user_id"], "MANDOCPRIORITY","READ") || is_access(session["user_id"], "MANDOCFROMS","READ") || is_access(session["user_id"], "FORMS","READ") || is_access(session["user_id"], "SFTRAINING","READ") || is_access(session["user_id"], "ASSETS","READ") %>
              <li class="nav-item">
                <a class="nav-link dropdown-indicator" href="#Document_management" role="button" data-bs-toggle="collapse" aria-controls="Document_management">
                  <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp iquan_ly_van_ban"></span></span><span class="nav-link-text ps-1">   <%= lib_translate("Document_Management_menu")  %>   </span>
                  </div>
                </a>
              </li>
            <% end %>
            <ul class="nav collapse <%= if ('/mandocs/incoming/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/mandocs/outgoing/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/mandocs/process/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/mandocs/watch/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/mandocbook/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/mandoctype/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/mandocpriority/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/mandocfroms/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/forms/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/released_mandocs/incoming/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/released_mandocs/outgoing/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/mandocs/search/index' == request.env['PATH_INFO'])
              'show'
              end %>" id="Document_management">
              <%  deparment_login= get_department_from_user_login(session[:user_id])
                incoming_count = 0
                outgoing_count = 0
                process_count = 0
                arrMandocProcess = []
                arrMandocProcessKn = []
              if !deparment_login.nil? 
                oMandocuhandlesProcess = Mandocuhandle.where(user_id: session[:user_id]).where.not(status: "DAXULY")
                oMandocuhandlesProcess.each do |manprocess| 
                    if !manprocess.mandocdhandle.nil?
                      if !manprocess.mandocdhandle.mandoc.nil?
                          mandoc_id = manprocess.mandocdhandle.mandoc.id
                          if manprocess.sothers == "THU_KY_HIEU_TRUONG"
                            arrMandocProcessKn.push(mandoc_id).uniq
                          else
                            arrMandocProcess.push(mandoc_id).uniq
                          end
                      end
                    end
                end            

                oUserORG = Uorg.where(user_id: session[:user_id]).first
                
                if !oUserORG.nil?
                  organization_id = oUserORG.organization_id
                  oMandocProcess = Mandoc.where(status: "INPROGRESS", id: arrMandocProcess, organization_id: organization_id)
                  
                  incoming_count = oMandocProcess.where("sfrom IS NOT NULL").count
                  outgoing_count = oMandocProcess.where("sfrom IS NULL").count
                  process_count = arrMandocProcessKn.count                                                     
                end              
                
              end%>
              <%# Văn bản đến %>
              <% if is_access(session["user_id"], "MANDOCS-INCOMING","READ") %>
                <li class="nav-item">
                  <a class="nav-link <%= 'active' if '/mandocs/incoming/index' == request.env['PATH_INFO'] %>" href="<%= mandocs_incoming_index_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp ivan_ban_den" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Document_to")  %></span> 
                      <% if incoming_count > 0%>
                        <span id="notifi_number" class="text-span"><%= incoming_count%></span>
                      <%end%>
                    </div>
                  </a>
                </li>
              <% end %>
                <%# Văn bản đi  %>
              <% if is_access(session["user_id"], "MANDOCS-OUTGOING","READ") %>
                <li class="nav-item">
                  <a class="nav-link <%= 'active' if '/mandocs/outgoing/index' == request.env['PATH_INFO'] %>" href="<%= mandocs_outgoing_index_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp ivan_ban_di" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Document_go")  %></span>
                      <% if outgoing_count > 0%>
                        <span id="notifi_number" class="text-span"><%= outgoing_count%></span>
                      <%end%>
                    </div>
                  </a>
                </li>
              <% end %>
              <%# Trang tìm kiếm văn bản %>
              <% if is_access(session["user_id"], "MANDOCS-SEARCH","READ") %>
                <li class="nav-item">
                  <a class="nav-link <%= 'active' if '/mandocs/search/index' == request.env['PATH_INFO'] %>" href="<%= mandocs_search_index_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-search" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Search")  %></span>
                    </div>
                  </a>
                </li>
              <% end %>
              <%# Văn bản cần xử lý %>
              <% if is_access(session["user_id"], "MANDOCS-PROCESS","READ") %>
                <li class="nav-item">
                  <a class="nav-link <%= 'active' if '/mandocs/process/index' == request.env['PATH_INFO'] %>" href="<%= mandocs_process_index_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp ivan_ban_can_xu_ly" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Documents_to_be_processed")%></span>
                      <% if process_count > 0%>
                        <span id="notifi_number" class="text-span"><%= process_count%></span>
                      <%end%>
                    </div>
                  </a>
                </li>
              <% end %>
                <!-- Document Management Config -->
                <% if is_access(session["user_id"], "MANDOCBOOK","READ") || is_access(session["user_id"], "MANDOCTYPE","READ") || is_access(session["user_id"], "MANDOCPRIORITY","READ") || is_access(session["user_id"], "MANDOCFROMS","READ") || is_access(session["user_id"], "FORMS","READ")%>
                  <li class="nav-item">
                    <li class="nav-item">
                      <a class="nav-link dropdown-indicator" href="#Document_management_config" role="button" data-bs-toggle="collapse" aria-controls="Document_management_config">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-wrench"></span></span><span class="nav-link-text ps-1">   <%= lib_translate("Config")  %>   </span>
                        </div>
                      </a>
                    </li>
                    <ul class="nav collapse <%= if ('/mandocbook/index' == request.env['PATH_INFO'])
                    'show'
                    elsif ('/mandoctype/index' == request.env['PATH_INFO'])
                    'show'
                    elsif ('/mandocpriority/index' == request.env['PATH_INFO'])
                    'show'
                    elsif ('/mandocfroms/index' == request.env['PATH_INFO'])
                    'show'
                    elsif ('/forms/index' == request.env['PATH_INFO'])
                    'show'
                    end %>" id="Document_management_config">
                      <% if is_access(session["user_id"], "MANDOCBOOK","READ") %>
                        <li class="nav-item"><a class="nav-link <%= 'active' if '/mandocbook/index' == request.env['PATH_INFO'] %>" href="<%= mandocbook_index_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-book" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Mandoc_Book")  %></span>
                          </div>
                          </a>
                        </li>
                      <% end %>
                      <% if is_access(session["user_id"], "MANDOCTYPE","READ") %>
                        <li class="nav-item"><a class="nav-link <%= 'active' if '/mandoctype/index' == request.env['PATH_INFO'] %>" href="<%= mandoctype_index_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp iloai_so" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Mandoc_Type")  %></span>
                          </div>
                          </a>
                        </li>
                      <% end %>
                      <% if is_access(session["user_id"], "MANDOCPRIORITY","READ") %>
                        <li class="nav-item"><a class="nav-link <%= 'active' if '/mandocpriority/index' == request.env['PATH_INFO'] %>" href="<%= mandocpriority_index_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp iloai_van_ban" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Mandoc_Priority")  %></span>
                          </div>
                          </a>
                        </li>
                      <% end %>
                      <% if is_access(session["user_id"], "MANDOCFROMS","READ") %>
                        <li class="nav-item"><a class="nav-link <%= 'active' if '/mandocfroms/index' == request.env['PATH_INFO'] %>" href="<%= mandocfroms_index_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="from fas fa-city" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Mandocfroms")  %></span>
                          </div>
                          </a>
                        </li>
                      <% end %>
                      <% if is_access(session["user_id"], "FORMS","READ") %>
                        <li class="nav-item"><a class="nav-link <%= 'active' if '/forms/index' == request.env['PATH_INFO'] %>" href="<%= forms_index_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-window-restore" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Form")  %></span>
                          </div>
                          </a>
                        </li>
                      <% end %>
                    </ul>
                  </li>
                <% end %>
                <!--end Document Management Config -->
                <!-- Released Mandocs -->
                <% if is_access(session["user_id"], "RELEASED-MANDOCS","READ")%>
                  <% 
                    oUserORG = Uorg.where(user_id: session[:user_id]).first
                    count_to = 0
                    if !oUserORG.nil?
                      organization_id = oUserORG.organization_id  
                      count_to = Mandoc.where(organization_id: organization_id).where("sfrom IS NOT NULL AND publish_to_staffs IS NULL AND publish_to_departments IS NULL").where(status: "INACTIVE").count
                    end
                  %>
                  <li class="nav-item">
                    <li class="nav-item">
                      <a class="nav-link dropdown-indicator" href="#Released_mandocs" role="button" data-bs-toggle="collapse" aria-controls="Released_mandocs">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp ilich_su_ban_hanh"></span></span>
                          <div class="<%= count_to > 0 ? "notifi_mandoc_acss d-inline-block notification-indicator notification-indicator-danger fa-icon-wait" : ""%> "><span class="nav-link-text ps-1 " > <%= lib_translate("Released_history")  %> </span></div>
                        </div>
                      </a>
                    </li>
                    <ul class="nav collapse <%= if ('/released_mandocs/outgoing/index' == request.env['PATH_INFO'])
                      'show'
                      elsif ('/released_mandocs/incoming/index' == request.env['PATH_INFO'])
                      'show'                                
                      end %>" id="Released_mandocs">
                        <% if is_access(session["user_id"], "RELEASED-MANDOCS-INCOMING","READ") %>
                          <li class="nav-item"><a class="nav-link <%= 'active' if '/released_mandocs/incoming/index' == request.env['PATH_INFO'] %>" href="<%= released_mandocs_incoming_index_path(lang: session[:lang]) %>">
                            <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp ilich_su_van_ban_den" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Document_to")  %>
                              <span id="notifi_number" class="text-span m-0 <%= count_to > 0 ? '' : 'd-none' %>"><%= count_to %></span>
                            </span>
                            </div>
                            </a>
                          </li>
                        <% end %>
                        <% if is_access(session["user_id"], "RELEASED-MANDOCS-OUTGOING","READ") %>
                          <li class="nav-item"><a class="nav-link <%= 'active' if '/released_mandocs/outgoing/index' == request.env['PATH_INFO'] %>" href="<%= released_mandocs_outgoing_index_path(lang: session[:lang]) %>">
                            <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp ilich_su_van_ban_di" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Document_go")  %></span>
                            </div>
                            </a>
                          </li>
                        <% end %>                     
                    </ul>
                  </li>
                <% end %>
                <!-- Released Mandocs -->
            </ul>
            <%#  ================================================ end Quản lý văn bản end  ================================================ %>
            <%#  ================================================ Quản lý nghỉ phép  ================================================%>
            <% 
                organization = false
                oUserORG = Uorg.where(user_id: session[:user_id]).first
                if !oUserORG.nil?
                  organization_id = oUserORG.organization_id
                  if organization_id.present?
                      scode = Organization.where(id: organization_id).first&.scode
                      if scode.present? && scode == "BUH"
                          organization = true
                      end
                  end
                end 
              %>
            <%# <% if is_access(session["user_id"], "LEAVE-INDEX","READ")%> 
              <li class="nav-item">
                <a class="nav-link dropdown-indicator" href="#Leave_management" role="button" data-bs-toggle="collapse" aria-controls="Leave_management">
                  <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-umbrella-beach" ></span></span><span class="nav-link-text ps-1">   <%= lib_translate("Quản lý nghỉ phép")  %>   </span>
                  </div>
                </a>
              </li>
            <%# <% end %>
            <ul class="nav collapse <%= if ('/leave_request/index' == request.env['PATH_INFO'])
              'show'
              elsif ('/manager_leave/management' == request.env['PATH_INFO'])
              'show'
              elsif ('/manager_leave/history' == request.env['PATH_INFO'])
              'show'
              elsif ('/manager_leave/position_leave' == request.env['PATH_INFO'])
              'show'
              elsif ('/manager_leave/manager_holiday' == request.env['PATH_INFO'])
              'show'
              elsif ('/holtypes' == request.env['PATH_INFO'])
              'show'
              elsif ('/holtemps' == request.env['PATH_INFO'])
              'show'
              elsif ('/setting_holidays/index' == request.env['PATH_INFO'])
              'show'
              end %>" id="Leave_management">
              <%# Trang đăng ký %>
              <% if is_access(session["user_id"], "LEAVE-INDEX","READ")%>
                <li class="nav-item">
                  <a class="nav-link <%= 'active' if '/leave_request/index' == request.env['PATH_INFO'] %>" href="<%= leave_request_index_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-edit" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Đăng ký nghỉ phép")  %></span> 

                    </div>
                  </a>
                </li>
              <% end %>
              <%# Trang xử lý %>
              <% if is_access(session["user_id"], "LEAVE-MANAGEMENT","READ") %>
                <li class="nav-item">
                  <a class="nav-link <%= 'active' if '/manager_leave/management' == request.env['PATH_INFO'] %>" href="<%= manager_leave_management_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-chart-bar" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Tác vụ xử lý ")  %></span>

                    </div>
                  </a>
                </li>
              <% end %>
              <%# Trang lịch sử %>
              <% if is_access(session["user_id"], "LEAVE-HISTORY","READ") %>
                <li class="nav-item d-none">
                  <a class="nav-link <%= 'active' if '/manager_leave/history' == request.env['PATH_INFO'] %>" href="<%= manager_leave_history_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-calendar-alt" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Lịch sử nghỉ phép")  %></span>
                    </div>
                  </a>
                </li>
              <% end %>
              <%# Cấu hình phép theo vị trí công việc %>
              <% if is_access(session["user_id"], "LEAVE-POSITION","READ") %>
                <li class="nav-item">
                  <a class="nav-link <%= 'active' if '/manager_leave/position_leave' == request.env['PATH_INFO'] %>" href="<%= manager_leave_position_leave_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-list-alt" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Phép theo VTCV")  %></span>

                    </div>
                  </a>
                </li>
              <% end %>
              <% if is_access(session["user_id"], "HOL-TYPES","READ") %>
                <li class="nav-item">
                  <a class="nav-link <%= 'active' if '/holtypes' == request.env['PATH_INFO'] %>" href="<%= holtypes_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fab fa-buffer" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Loại nghỉ phép")  %></span>

                    </div>
                  </a>
                </li>
              <% end %>
              <% if is_access(session["user_id"], "HOL-TEMPS","READ") %>
                <li class="nav-item d-none">
                  <a class="nav-link <%= 'active' if '/holtemps' == request.env['PATH_INFO'] %>" href="<%= holtemps_path(lang: session[:lang]) %>">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-align-justify" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Biểu mẫu phép")  %></span>

                    </div>
                  </a>
                </li>
              <% end %>
              <% if is_access(session["user_id"], "LEAVE-HISTORY","READ") %>
              <li class="nav-item">
                <a class="nav-link <%= 'active' if '/setting_holidays/index' == request.env['PATH_INFO'] %>" href="<%= setting_holidays_index_path(lang: session[:lang]) %>">
                  <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-user-cog" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Cấu hình ngày nghỉ")  %></span>

                  </div>
                </a>
              </li>
              <%# Trang quản lý nghỉ phép đơn vị %>
              
              <li class="nav-item">
                <a class="nav-link <%= 'active' if '/manager_leave/manager_holiday' == request.env['PATH_INFO'] %>" href="<%= manager_leave_manager_holiday_path(lang: session[:lang]) %>">
                  <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-chart-pie" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Báo cáo thống kê")  %></span>

                  </div>
                </a>
              </li>
              <% end %>
            </ul>
            <%#  ================================================ end Quản lý nghỉ phép end  ================================================ %>
            <% if is_access(session["user_id"], "SFTRAINING","READ") %>
              <li class="nav-item">
                <form action="<%= redirect_to_straining_path %>" method="post">
                  <div class="d-flex" style="align-items: center;">
                    <button class="nav-link" style="border: none; background: unset;text-align: left;" id="btn_redirect" type="submit">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-chalkboard-teacher" ></span></span><span class="nav-link-text ps-1">   <%= lib_translate("Training_Management")  %> 
                        </span>  
                      </div>
                    </button>
                    &nbsp;
                    <% sNotices = Snotice.where(user_id: session[:user_id]).pluck(:isread)
                            checkNoti = false
                            falseCount = 0

                            if sNotices.include?(false)
                              checkNoti = false
                              falseCount = sNotices.count(false)
                            else
                              checkNoti = true
                            end
                          %>
                    <div class="<%= falseCount > 0 ? '' : 'd-none' %>">
                      <span id="notifi_number" class="text-span m-0"><%= falseCount %></span>
                    </div>
                  </div>
                </form>
              </li>
            <% end %>
            <% if is_access(session["user_id"], "ASSETS","READ") %>
              <li class="nav-item">
                <form action="<%= redirect_to_assets_path %>" method="post">
                  <input type="hidden" name="system" value="masset">
                  <input type="hidden" name="resource" value="ASSETS">
                  <div class="d-flex" style="align-items: center;">
                    <button class="nav-link" style="border: none; background: unset;text-align: left;" id="btn_redirect" type="submit">
                      <div class="d-flex align-items-center">
                        <!-- <span class="fas fa-laptop-house fs-0"></span> -->
                        <span class="ierp iBMU" ></span>
                        <span class="nav-link-text ps-2"><%= lib_translate("ASSETS")  %> (BMU)</span>  
                      </div>
                    </button>
                  </div>
                </form>
              </li>
            <% end %>
            <% if is_access(session["user_id"], "HASSETS","READ") %>
              <li class="nav-item">
                <form action="<%= redirect_to_assets_path %>" method="post">
                  <input type="hidden" name="system" value="hasset">
                  <input type="hidden" name="resource" value="HASSETS">
                  <div class="d-flex" style="align-items: center;">
                    <button class="nav-link" style="border: none; background: unset;text-align: left;" id="btn_redirect" type="submit">
                      <div class="d-flex align-items-center">
                        <span class="ierp iBUH" ></span>
                        <span class="nav-link-text ps-2"><%= lib_translate("ASSETS")  %> (BUH)</span>  
                      </div>
                    </button>
                  </div>
                </form>
              </li>
            <% end %>
            <!-- Quản lý website -->
            <% if is_access(session["user_id"], "WEBSITE","READ") %>
              <li class="nav-item">
                <li class="nav-item">
                  <a class="nav-link dropdown-indicator" href="#Website_manager" role="button" data-bs-toggle="collapse" aria-controls="Website_manager">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="ierp iquan_ly_website"></span></span><span class="nav-link-text ps-1">Quản lý Website</span></div>
                  </a>
                </li>
                <ul class="nav collapse" id="Website_manager">
                  <!-- Nha khoa -->
                  <% if is_access(session["user_id"], "DENTAL","READ") || is_access(session["user_id"], "ADMIN-NHA-KHOA","READ")  %>
                    <li class="nav-item">
                      <form action="<%= redirect_to_nha_khoa_path %>" method="post">
                          <button class="nav-link" style="border: none; background: unset;text-align: left;" id="btn_redirect" type="submit">
                            <div class="d-flex align-items-center">
                              <span class="nav-link-icon">
                                <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
                                  width="22" height="22" viewBox="0 0 128.000000 128.000000"
                                  preserveAspectRatio="xMidYMid meet">

                                  <g transform="translate(0.000000,128.000000) scale(0.100000,-0.100000)"
                                  fill="currentcolor" stroke="none">
                                    <path d="M79 1248 c-8 -18 -27 -39 -43 -48 -42 -22 -42 -36 -3 -55 20 -9 40
                                    -29 47 -46 15 -35 37 -38 49 -6 10 25 37 51 64 60 26 10 20 33 -12 47 -17 7
                                    -37 26 -46 46 -20 43 -37 43 -56 2z m49 -71 c4 -21 -25 -34 -40 -19 -8 8 -8
                                    16 2 27 16 19 34 15 38 -8z"/>
                                    <path d="M321 1128 c-74 -28 -142 -97 -170 -171 -40 -108 -23 -250 50 -407 28
                                    -61 52 -142 84 -285 26 -114 53 -211 64 -225 36 -50 122 -48 155 2 8 13 31
                                    117 50 233 37 220 43 235 92 235 12 0 27 -6 33 -14 7 -8 28 -108 47 -223 19
                                    -114 41 -217 49 -229 34 -52 119 -54 156 -4 11 14 38 110 64 225 32 143 56
                                    224 84 285 101 218 94 398 -19 510 -84 85 -206 111 -325 70 -32 -11 -75 -20
                                    -95 -20 -20 0 -63 9 -95 20 -74 26 -155 25 -224 -2z m209 -38 c83 -25 136 -25
                                    224 1 143 43 262 -5 328 -131 45 -88 21 -288 -50 -410 -18 -31 -42 -115 -77
                                    -265 -46 -202 -52 -220 -76 -234 -24 -13 -29 -13 -51 1 -22 15 -27 33 -57 215
                                    -36 213 -48 255 -79 272 -36 19 -91 13 -117 -13 -29 -29 -27 -21 -65 -255 -17
                                    -101 -35 -192 -40 -202 -14 -26 -47 -33 -74 -16 -19 13 -29 47 -70 228 -34
                                    150 -57 229 -76 264 -60 106 -92 273 -71 362 29 116 131 199 251 202 19 0 64
                                    -8 100 -19z"/>
                                    <path d="M330 1013 c-45 -17 -90 -83 -90 -133 0 -23 5 -30 19 -30 14 0 20 10
                                    25 39 5 36 51 91 76 91 4 0 6 9 3 20 -6 21 -8 22 -33 13z"/>
                                    <path d="M410 1000 c0 -13 7 -20 20 -20 13 0 20 7 20 20 0 13 -7 20 -20 20
                                    -13 0 -20 -7 -20 -20z"/>
                                    <path d="M1150 355 c-5 -14 -26 -35 -45 -46 -43 -26 -44 -43 -4 -60 20 -8 37
                                    -25 46 -46 16 -39 30 -39 53 2 9 16 29 36 46 45 36 20 37 37 4 53 -14 7 -34
                                    27 -45 44 -24 39 -42 41 -55 8z m39 -91 c-14 -14 -17 -14 -29 0 -10 13 -10 19
                                    0 32 12 14 15 14 29 0 14 -15 14 -17 0 -32z"/>
                                  </g>
                                </svg>
                              </span>
                              <span class="nav-link-text ps-1">Nha khoa</span>
                            </div>
                          </button>
                      </form>
                    </li>
                  <% end %>
                  <!-- da lieu -->
                  <% if is_access(session["user_id"], "DA-LIEU","READ") || is_access(session["user_id"], "ADMIN-DA-LIEU","READ") %>
                    <li class="nav-item">
                      <form action="<%= redirect_to_da_lieu_path %>" method="post">
                        <button class="nav-link" style="border: none; background: unset;text-align: left;" id="btn_redirect" type="submit">
                          <div class="d-flex align-items-center">
                            <span class="nav-link-icon" style="width: 1.6rem;">
                              <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
                                width="24" height="24" viewBox="0 0 128 128"
                                preserveAspectRatio="xMidYMid meet">
                                <g transform="translate(0.000000,128.000000) scale(0.100000,-0.100000)"
                                fill="currentcolor" stroke="none">
                                <path d="M555 1266 c-87 -28 -225 -120 -225 -151 0 -25 21 -17 83 32 115 90
                                233 116 327 71 l45 -21 -46 -8 c-72 -11 -179 -74 -179 -105 0 -22 20 -17 77
                                23 67 46 153 58 191 26 29 -22 39 -86 23 -136 -27 -80 -151 -181 -348 -281
                                -145 -74 -225 -159 -254 -268 -7 -27 -17 -48 -21 -48 -19 0 -38 53 -49 138
                                -20 160 14 332 91 451 24 36 39 70 35 79 -18 46 -122 -136 -150 -263 -13 -58
                                -16 -107 -13 -208 6 -158 19 -202 67 -228 29 -15 38 -29 53 -77 33 -107 118
                                -199 235 -254 106 -50 209 -46 323 12 47 24 140 108 140 126 0 21 -33 17 -47
                                -5 -19 -31 -122 -96 -181 -116 -148 -48 -327 37 -412 196 -91 171 -24 311 208
                                435 59 32 127 70 151 86 l44 28 59 -32 c101 -54 177 -153 215 -283 17 -56 12
                                -134 -12 -192 -18 -43 -19 -76 -2 -71 19 7 44 67 58 141 10 56 17 70 38 80 34
                                16 60 99 60 193 1 59 -3 77 -23 106 -13 20 -27 59 -31 89 -9 70 -47 150 -91
                                188 -37 33 -49 37 -59 20 -4 -6 8 -25 27 -42 42 -39 73 -97 83 -157 7 -40 6
                                -43 -8 -31 -16 13 -37 8 -37 -9 0 -5 20 -23 45 -41 69 -50 79 -114 39 -241 -9
                                -27 -22 -48 -29 -48 -7 0 -21 24 -30 53 -39 118 -114 214 -213 270 l-53 29 54
                                57 c65 68 93 132 83 193 -24 149 -188 242 -341 194z"/>
                                <path d="M413 604 c-64 -32 -90 -111 -58 -174 48 -93 182 -93 230 0 31 61 6
                                139 -56 172 -41 22 -74 23 -116 2z m118 -53 c49 -50 37 -121 -27 -147 -95 -40
                                -172 84 -93 150 41 35 83 34 120 -3z"/>
                                <path d="M404 499 c-21 -34 82 -57 114 -25 23 23 2 42 -28 26 -17 -9 -29 -9
                                -47 -1 -30 14 -30 14 -39 0z"/>
                                <path d="M764 606 c-126 -55 -85 -246 53 -246 71 0 126 55 126 126 0 50 -21
                                87 -66 114 -37 23 -70 25 -113 6z m102 -44 c32 -25 46 -59 39 -91 -20 -91
                                -143 -99 -176 -12 -30 77 74 155 137 103z"/>
                                <path d="M754 498 c-11 -17 25 -38 65 -38 44 0 66 13 58 34 -5 13 -11 14 -32
                                4 -19 -8 -31 -8 -50 0 -32 15 -32 15 -41 0z"/>
                                <path d="M558 255 c-87 -46 -51 -79 88 -79 71 0 97 3 118 17 32 22 24 37 -34
                                67 -50 25 -119 24 -172 -5z m145 -21 c41 -12 11 -24 -60 -24 -69 1 -76 2 -59
                                15 18 13 84 18 119 9z"/>
                                </g>
                              </svg>
                            </span>
                          <span class="nav-link-text ps-1">Da liễu</span>
                          </div>
                        </button>
                      </form>
                    </li>    
                  <% end %>
                  <!-- Phau thuat tham my -->
                  <% if is_access(session["user_id"], "PHAU-THUAT-THAM-MY","READ") ||  is_access(session["user_id"], "ADMIN-PHAU-THUAT-THAM-MY","READ")  %>
                    <li class="nav-item">
                      <form action="<%= redirect_to_tham_my_path %>" method="post">
                        <button class="nav-link" style="border: none; background: unset;text-align: left;" id="btn_redirect" type="submit">
                          <div class="d-flex align-items-center">
                            <span class="nav-link-icon">
                              <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
                                width="22" height="22" viewBox="0 0 128.000000 128.000000"
                                preserveAspectRatio="xMidYMid meet">

                                <g transform="translate(0.000000,128.000000) scale(0.100000,-0.100000)"
                                fill="currentcolor" stroke="none">
                                  <path d="M210 1267 c0 -15 40 -76 92 -138 l38 -47 -43 -6 c-51 -8 -100 -36
                                  -123 -71 -26 -40 -7 -42 28 -3 62 68 140 76 223 22 61 -38 149 -74 233 -94
                                  l52 -12 0 -145 0 -144 -22 7 c-31 9 -120 57 -158 84 -32 23 -30 24 -44 -24 -5
                                  -14 74 -108 151 -181 24 -23 43 -46 43 -53 0 -7 -14 -41 -31 -75 -24 -50 -62
                                  -93 -192 -219 -88 -87 -158 -161 -154 -164 7 -7 210 179 286 262 26 28 60 77
                                  77 108 16 31 32 56 35 56 4 0 17 -8 29 -18 22 -18 22 -19 5 -53 -29 -55 -32
                                  -172 -6 -239 22 -57 66 -119 76 -108 3 3 -8 27 -25 54 -37 59 -50 103 -50 170
                                  0 46 25 144 37 144 3 0 75 -55 161 -123 220 -173 216 -171 246 -163 34 8 80
                                  78 73 108 -5 19 -201 182 -375 314 -13 9 -13 17 2 59 20 61 21 154 1 202 -40
                                  96 -101 143 -236 179 -173 47 -299 135 -383 267 -37 58 -46 67 -46 44z m572
                                  -397 c77 -63 111 -196 74 -285 -16 -38 -27 -43 -46 -20 -7 9 -20 13 -28 10 -8
                                  -3 -23 2 -33 11 -17 15 -19 34 -19 166 0 81 3 148 8 148 4 0 24 -13 44 -30z
                                  m-243 -183 c6 -8 8 -17 4 -20 -7 -8 -33 11 -33 24 0 13 16 11 29 -4z m111 -67
                                  c0 -4 -9 -15 -20 -25 -20 -18 -21 -18 -50 10 -23 22 -27 32 -19 47 10 18 11
                                  18 50 -4 21 -12 39 -25 39 -28z m83 -47 l27 -22 -28 -32 -29 -32 -36 29 c-21
                                  16 -37 33 -37 37 0 5 10 21 23 37 24 29 22 30 80 -17z m272 -188 c115 -91 214
                                  -173 218 -181 11 -20 -9 -61 -40 -82 -21 -13 -26 -12 -49 3 -66 43 -419 329
                                  -416 338 4 13 64 87 71 87 3 0 100 -74 216 -165z"/>
                                  <path d="M860 452 c0 -9 165 -141 187 -149 35 -13 3 19 -82 86 -88 69 -105 79
                                  -105 63z"/>
                                  <path d="M1095 271 c-3 -5 8 -19 25 -31 20 -15 30 -17 30 -8 -1 17 -47 51 -55
                                  39z"/>
                                  <path d="M880 1205 c0 -3 8 -22 19 -43 32 -64 44 -138 37 -221 -6 -59 -4 -73
                                  5 -64 18 18 23 143 9 204 -15 62 -46 129 -60 129 -6 0 -10 -2 -10 -5z"/>
                                  <path d="M420 1183 c1 -16 71 -103 95 -119 41 -27 43 -6 3 26 -20 17 -46 46
                                  -58 65 -20 32 -40 46 -40 28z"/>
                                  <path d="M854 955 c7 -42 25 -89 32 -82 10 10 -5 72 -22 93 -15 18 -15 18 -10
                                  -11z"/>
                                  <path d="M442 846 c-67 -43 -131 -53 -192 -32 -53 18 -60 19 -60 6 0 -18 108
                                  -42 160 -35 43 6 50 4 60 -14 6 -12 15 -19 20 -16 6 4 5 14 -2 25 -18 28 -4
                                  34 33 16 40 -20 52 -11 20 15 l-23 19 24 0 c13 0 34 -7 46 -16 17 -12 22 -12
                                  22 -2 0 7 -12 21 -27 30 l-28 17 27 1 c15 0 38 -4 52 -10 16 -6 27 -6 31 0 8
                                  13 -38 30 -78 30 -19 0 -54 -14 -85 -34z"/>
                                  <path d="M186 728 c-24 -33 5 -75 44 -63 28 9 35 43 14 66 -22 25 -39 24 -58
                                  -3z m47 -15 c9 -9 -13 -34 -24 -27 -14 8 -11 34 4 34 8 0 17 -3 20 -7z"/>
                                  <path d="M338 699 c-24 -14 -23 -56 2 -69 42 -22 82 34 48 68 -14 14 -27 15
                                  -50 1z m42 -35 c0 -8 -9 -14 -20 -14 -22 0 -28 26 -7 33 16 6 27 -1 27 -19z"/>
                                  <path d="M110 464 c-14 -8 -37 -11 -53 -8 -15 3 -27 1 -27 -4 0 -19 57 -24 89
                                  -7 17 8 31 20 31 25 0 13 -9 11 -40 -6z"/>
                                  <path d="M83 343 c-18 -2 -33 -9 -33 -15 0 -7 15 -9 42 -5 47 6 80 -7 117 -46
                                  l23 -26 -26 -26 c-32 -32 -85 -55 -126 -55 -16 0 -30 -5 -30 -11 0 -8 17 -10
                                  50 -7 57 6 118 41 139 82 14 25 13 28 -15 57 -32 34 -76 60 -96 57 -7 0 -28
                                  -3 -45 -5z"/>
                                  <path d="M60 250 c0 -5 18 -10 40 -10 22 0 40 5 40 10 0 6 -18 10 -40 10 -22
                                  0 -40 -4 -40 -10z"/>
                                </g>
                              </svg>
                            </span>
                            <span class="nav-link-text ps-1">Thẩm mỹ</span>
                          </div>
                        </button>
                      </form>
                    </li>      
                  <% end %>

                  <!-- Sam Viet Han -->
                  <% if is_access(session["user_id"], "SAM-VIET-HAN","READ") || is_access(session["user_id"], "ADMIN-SAM-VIET-HAN","READ") %>
                    <li class="nav-item">
                      <form action="<%= redirect_to_sam_viet_han_path %>" method="post">
                        <button class="nav-link" style="border: none; background: unset;text-align: left;" id="btn_redirect" type="submit">
                          <div class="d-flex align-items-center">
                            <span class="nav-link-icon" style="width: 1.6rem;">
                              <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
                              width="22" height="22" viewBox="0 0 128.000000 128.000000"
                              preserveAspectRatio="xMidYMid meet">
                              <g transform="translate(0.000000,128.000000) scale(0.100000,-0.100000)"
                              fill="currentcolor" stroke="none">
                                <path d="M851 1203 c-98 -80 -141 -142 -141 -208 l0 -30 -29 36 c-48 58 -122
                                92 -214 97 -74 4 -79 3 -74 -14 3 -11 9 -44 12 -74 18 -146 111 -243 247 -258
                                55 -6 64 -10 72 -33 7 -21 4 -32 -13 -56 l-22 -30 -52 18 c-29 10 -78 21 -109
                                24 -56 7 -59 9 -99 63 -22 32 -64 93 -92 137 -58 88 -93 114 -158 118 -29 1
                                -50 9 -65 25 -30 29 -59 28 -89 -3 -26 -25 -33 -63 -14 -82 9 -9 9 -20 0 -44
                                -21 -61 -6 -147 37 -206 17 -23 17 -30 5 -77 -19 -69 -10 -150 28 -244 17 -42
                                34 -104 37 -137 6 -51 10 -60 27 -60 16 0 20 6 18 27 -1 23 2 27 20 25 32 -3
                                34 29 2 46 -49 25 -111 213 -99 295 7 45 7 45 24 24 10 -12 21 -42 24 -67 16
                                -105 58 -193 131 -275 30 -34 44 -61 54 -103 10 -47 16 -57 33 -57 18 0 19 4
                                14 37 l-7 38 43 -45 c24 -25 53 -59 64 -75 18 -24 25 -28 35 -18 21 21 -6 58
                                -132 183 -75 74 -126 133 -142 165 -44 88 -40 93 26 30 71 -68 143 -110 269
                                -155 99 -36 170 -79 276 -168 41 -34 77 -62 82 -62 4 0 12 6 18 13 9 10 -11
                                31 -88 93 -116 93 -187 134 -290 169 -155 52 -246 125 -347 278 -87 132 -133
                                221 -133 258 0 38 22 82 56 108 32 25 109 29 141 6 12 -8 55 -66 95 -127 116
                                -176 205 -262 367 -358 68 -39 105 -73 137 -125 l19 -30 -30 19 c-16 10 -42
                                30 -58 44 -21 20 -30 23 -39 14 -19 -19 46 -79 129 -118 81 -39 117 -70 67
                                -58 -16 4 -51 15 -79 25 -63 24 -75 24 -75 -1 0 -15 18 -25 93 -49 87 -28 213
                                -51 278 -51 22 0 29 5 29 19 0 14 -10 20 -46 25 -26 3 -60 6 -75 6 -19 0 -37
                                9 -52 27 -24 29 -52 55 -82 77 -31 22 -13 28 35 11 23 -8 60 -15 81 -15 47 0
                                119 27 119 45 0 23 -16 26 -54 10 -49 -21 -85 -19 -159 9 -44 17 -79 39 -113
                                72 -27 27 -87 72 -134 101 -47 28 -103 68 -125 88 l-40 36 45 -6 c69 -9 120
                                -38 225 -131 94 -82 160 -124 218 -138 23 -5 27 -3 27 14 0 11 -8 23 -17 27
                                -10 3 -34 13 -53 21 l-35 15 33 10 c21 6 32 16 32 28 0 23 -14 24 -66 5 -39
                                -13 -40 -13 -83 24 l-43 37 36 6 c19 3 51 13 70 22 30 14 33 19 24 35 -11 17
                                -13 17 -52 -1 -49 -22 -115 -24 -142 -5 -31 23 -6 48 66 68 l62 17 51 -26
                                c108 -54 197 -41 305 48 l52 43 -47 48 c-71 72 -157 95 -234 64 -13 -6 -10 6
                                12 50 43 84 40 132 -17 263 -26 58 -47 107 -48 108 0 1 -43 -33 -95 -74z m109
                                -60 c54 -118 50 -188 -13 -259 -29 -34 -84 -70 -93 -61 -2 2 1 17 6 33 7 21
                                21 34 46 43 41 15 45 48 5 43 -20 -2 -22 1 -17 23 4 14 9 45 13 70 5 40 4 45
                                -14 45 -16 0 -22 -9 -27 -41 -6 -39 -8 -41 -25 -25 -16 15 -19 15 -31 1 -11
                                -14 -10 -20 12 -42 l26 -25 -20 -61 -21 -60 -18 24 c-23 30 -39 83 -39 129 1
                                60 33 114 106 174 37 31 69 56 71 56 1 0 17 -30 33 -67z m-383 -112 c66 -34
                                113 -97 127 -171 l6 -35 -27 35 c-14 19 -49 57 -76 83 -46 44 -52 47 -66 32
                                -15 -14 -13 -18 17 -41 30 -24 31 -27 16 -38 -25 -19 -12 -40 25 -40 25 -1 39
                                -9 58 -33 l26 -33 -24 0 c-39 0 -123 45 -153 81 -36 42 -51 79 -61 143 l-7 49
                                50 -6 c28 -3 68 -15 89 -26z m-487 -50 c0 -3 -9 -12 -20 -18 -23 -15 -38 1
                                -20 22 11 13 40 10 40 -4z m688 -208 c-2 -9 -9 -18 -15 -21 -9 -2 -13 9 -13
                                39 0 42 0 43 16 21 8 -12 14 -30 12 -39z m355 43 c18 -7 43 -25 56 -40 l25
                                -26 -29 -30 c-41 -43 -103 -64 -161 -55 -103 15 -90 35 24 35 71 0 72 0 72 26
                                0 23 -3 25 -26 20 -22 -6 -25 -4 -22 16 5 33 -29 36 -43 4 -7 -14 -20 -26 -29
                                -26 -10 0 -32 -3 -51 -7 l-33 -6 24 33 c25 36 89 69 133 70 15 0 42 -6 60 -14z
                                m-242 -54 c-26 -37 -40 -48 -73 -56 l-28 -6 16 34 c11 22 27 37 48 44 53 17
                                59 15 37 -16z"/>
                                <path d="M202 853 c-32 -15 -92 -76 -92 -92 0 -5 7 -12 16 -15 10 -4 25 5 41
                                24 14 17 38 37 54 46 17 8 29 23 29 34 0 24 -5 24 -48 3z"/>
                                <path d="M262 723 c-18 -8 -46 -32 -63 -51 -29 -33 -30 -36 -14 -48 15 -11 21
                                -9 43 18 15 17 45 39 69 50 29 13 40 23 37 33 -8 19 -31 19 -72 -2z"/>
                                <path d="M375 628 c-35 -19 -66 -52 -86 -89 -18 -36 -18 -37 0 -42 14 -3 26 6
                                43 31 12 20 38 46 55 57 47 29 35 69 -12 43z"/>
                                <path d="M452 510 c-58 -35 -95 -120 -52 -120 11 0 20 6 20 14 0 20 64 86 84
                                86 9 0 16 8 16 20 0 26 -25 26 -68 0z"/>
                                <path d="M564 431 c-17 -10 -43 -34 -57 -52 -24 -30 -25 -35 -11 -46 13 -9 23
                                -3 59 33 25 24 52 44 61 44 11 0 14 5 10 16 -3 9 -6 18 -6 20 0 10 -27 3 -56
                                -15z"/>
                                <path d="M642 342 c-35 -33 -40 -47 -17 -56 8 -3 26 6 41 21 20 20 24 30 17
                                45 -8 19 -10 19 -41 -10z"/>
                                <path d="M876 123 c-9 -23 9 -37 77 -62 72 -26 87 -26 87 -1 0 14 -11 22 -42
                                31 -24 6 -59 19 -79 30 -34 17 -38 17 -43 2z"/>
                                </g>
                              </svg>
                            </span>
                            <span class="nav-link-text ps-1">Nhân Sâm Việt Hàn</span>
                          </div>
                        </button>
                      </form>
                    </li>  
                  <% end %>

                  <!-- Vien nghien cuu y sinh -->
                  <% if is_access(session["user_id"],"VIEN-NGHIEN-CUU", "READ") || is_access(session["user_id"],"ADMIN-VIEN-NGHIEN-CUU", "READ") %>
                    <li class="nav-item">
                      <form action="<%= redirect_to_vien_nghien_cuu_path %>" method="post">
                        <button class="nav-link" style="border: none; background: unset;text-align: left;" id="btn_redirect" type="submit">
                          <div class="d-flex align-items-center">
                            <span class="nav-link-icon" style="width: 1.6rem;">
                              <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
                                width="20" height="20" viewBox="0 0 128.000000 128.000000"
                                preserveAspectRatio="xMidYMid meet">
                                <g transform="translate(0.000000,128.000000) scale(0.100000,-0.100000)"
                                fill="currentcolor" stroke="none">
                                <path d="M32 1248 c-16 -16 -16 -120 0 -136 7 -7 25 -12 40 -12 l28 0 0 -460
                                0 -460 -28 0 c-15 0 -33 -5 -40 -12 -16 -16 -16 -120 0 -136 9 -9 155 -12 595
                                -12 l584 0 24 25 c36 35 32 72 -15 138 l-40 55 0 431 0 431 28 0 c15 0 33 5
                                40 12 16 16 16 120 0 136 -17 17 -1199 17 -1216 0z m1188 -68 l0 -40 -580 0
                                -580 0 0 40 0 40 580 0 580 0 0 -40z m-80 -482 l0 -402 -60 84 -59 85 -1 95
                                c0 85 2 97 20 108 23 14 28 80 8 100 -16 16 -200 16 -216 0 -20 -20 -15 -86 8
                                -100 18 -11 20 -24 20 -107 l0 -93 -101 -144 -101 -144 -259 0 -259 0 0 460 0
                                460 500 0 500 0 0 -402z m-120 22 c0 -18 -7 -20 -80 -20 -73 0 -80 2 -80 20 0
                                18 7 20 80 20 73 0 80 -2 80 -20z m-40 -80 c0 -13 -7 -20 -20 -20 -13 0 -20
                                -7 -20 -20 0 -13 7 -20 20 -20 13 0 20 -7 20 -20 0 -13 -7 -20 -20 -20 -13 0
                                -20 -7 -20 -20 0 -13 7 -20 20 -20 13 0 20 -7 20 -20 0 -13 -7 -20 -20 -20
                                -13 0 -20 -7 -20 -20 0 -16 7 -20 33 -20 26 0 37 -8 60 -40 l28 -40 -121 0
                                -121 0 40 57 41 56 0 104 0 103 40 0 c33 0 40 -3 40 -20z m65 -365 c-33 -32
                                -33 -78 0 -110 30 -31 77 -33 106 -4 20 20 21 20 34 2 57 -75 45 -97 -52 -101
                                l-70 -3 -6 31 c-13 63 -101 64 -113 0 l-6 -30 -127 0 c-179 0 -183 6 -86 146
                                l66 94 139 0 139 0 -24 -25z m87 -40 c8 -19 8 -28 -2 -40 -23 -28 -70 -12 -70
                                25 0 42 54 54 72 15z m-507 -135 l0 -40 -282 0 -283 0 0 40 0 40 283 0 282 0
                                0 -40z m391 -11 c10 -17 -13 -36 -27 -22 -12 12 -4 33 11 33 5 0 12 -5 16 -11z"/>
                                <path d="M560 1000 c0 -18 7 -20 70 -20 63 0 70 2 70 20 0 18 -7 20 -70 20
                                -63 0 -70 -2 -70 -20z"/>
                                <path d="M720 960 c-16 -16 -33 -20 -93 -20 -62 0 -78 -4 -107 -25 -42 -31
                                -49 -31 -110 0 l-50 25 -70 -35 -70 -35 0 -80 0 -80 60 -30 60 -30 0 -60 c0
                                -59 0 -60 -40 -80 -39 -20 -40 -21 -40 -75 0 -42 -5 -60 -20 -75 -25 -25 -25
                                -55 0 -80 25 -25 55 -25 80 0 25 25 25 55 0 80 -14 14 -20 33 -20 63 0 37 4
                                45 29 60 29 17 31 17 94 -14 l63 -31 65 35 64 35 3 80 3 80 -61 33 -60 34 0
                                57 c0 53 2 59 33 82 29 22 45 26 100 26 54 0 71 -4 87 -20 11 -11 29 -20 40
                                -20 11 0 29 9 40 20 11 11 20 29 20 40 0 11 -9 29 -20 40 -11 11 -29 20 -40
                                20 -11 0 -29 -9 -40 -20z m56 -31 c10 -17 -13 -36 -27 -22 -12 12 -4 33 11 33
                                5 0 12 -5 16 -11z m-366 -54 l50 -25 0 -60 0 -60 -50 -25 -51 -26 -49 28 -50
                                28 0 56 0 56 48 26 c26 15 48 27 50 27 1 0 25 -11 52 -25z m120 -200 l50 -25
                                0 -60 0 -60 -50 -25 -51 -26 -49 28 -50 28 0 55 0 55 48 27 c26 15 48 28 50
                                28 1 0 25 -11 52 -25z m-234 -346 c10 -17 -13 -36 -27 -22 -12 12 -4 33 11 33
                                5 0 12 -5 16 -11z"/>
                                <path d="M860 960 c0 -19 7 -20 120 -20 113 0 120 1 120 20 0 19 -7 20 -120
                                20 -113 0 -120 -1 -120 -20z"/>
                                <path d="M860 880 c0 -19 7 -20 120 -20 113 0 120 1 120 20 0 19 -7 20 -120
                                20 -113 0 -120 -1 -120 -20z"/>
                                <path d="M460 320 c0 -17 7 -20 40 -20 33 0 40 3 40 20 0 17 -7 20 -40 20 -33
                                0 -40 -3 -40 -20z"/>
                                <path d="M580 320 c0 -17 7 -20 40 -20 33 0 40 3 40 20 0 17 -7 20 -40 20 -33
                                0 -40 -3 -40 -20z"/>
                                <path d="M460 240 c0 -19 7 -20 100 -20 93 0 100 1 100 20 0 19 -7 20 -100 20
                                -93 0 -100 -1 -100 -20z"/>
                                <path d="M840 240 c-11 -11 -20 -29 -20 -40 0 -26 34 -60 60 -60 26 0 60 34
                                60 60 0 11 -9 29 -20 40 -11 11 -29 20 -40 20 -11 0 -29 -9 -40 -20z m56 -31
                                c10 -17 -13 -36 -27 -22 -12 12 -4 33 11 33 5 0 12 -5 16 -11z"/>
                                </g>
                              </svg>
                            </span>
                            <span class="nav-link-text ps-1">Viện nghiên cứu Y sinh ứng dụng</span>
                          </div>
                        </button>
                      </form>
                    </li>   
                  <% end %>

                </ul>
              </li>
            <%end%>
            <script>
              $('#btn_redirect').click(function(){
                  $( document ).ready(function () {
                    $('#btn_redirect').attr("type","button");
                  })
                })
            </script>
          </li>
          <!-- Nghiệp vụ -->
          <li class="nav-item">
            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label"><%= lib_translate("Nghiệp vụ")  %>
              </div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>
            <!-- Danh sách đơn vị -->
            <% if is_access(session["user_id"], "DEPARTMENTS","READ") %>
            <li class="nav-item">
              <a class="nav-link <%=  if ('/departments/department_list' == request.env['PATH_INFO'])
                'active'
                end %>"
                id="" href="<%= departments_department_list_path(lang: session[:lang]) %>" role="button">
                <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-file-alt"></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Department")  %></span>
                </div>
              </a>
            </li>
            <% end %>
            <!-- Tác vụ đơn vị -->
            <li class="nav-item">
              <a class="nav-link" id="" href="#" role="button">
                <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-business-time"></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Tác vụ đơn vị")  %></span>
                </div>
              </a>
            </li>
            <!-- Tác vụ nhân sự -->
            <li class="nav-item">
              <a class="nav-link" id="" href="<%= appointments_path(lang: session[:lang]) %>" role="button">
                <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-business-time"></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Tác vụ nhân sự")  %></span>
                </div>
              </a>
            </li>
          </li>
          <!-- Cấu hình -->
          <% if is_access(session["user_id"], "WORKS","READ") || is_access(session["user_id"], "POSITIONJOB-AUTHORITY","READ") %>
          <li class="nav-item">
            <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
              <div class="col-auto navbar-vertical-label"><%= lib_translate("Cấu Hình")  %>
              </div>
              <div class="col ps-0">
                <hr class="mb-0 navbar-vertical-divider" />
              </div>
            </div>
            <!-- Thư viện công việc -->
            <% if is_access(session["user_id"], "WORKS","READ") %>
              <li class="nav-item">
                <a class="nav-link <%=  if ('/works/index' == request.env['PATH_INFO'])
                  'active'
                  end %>"
                  id="" href="<%= works_index_path(lang: session[:lang]) %>" role="button">
                  <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-file-alt"></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Thư viện công việc")  %></span>
                  </div>
                </a>
              </li>
              <% end %>
              <!-- Cấp quyền hạn -->
              <% if is_access(session["user_id"], "POSITIONJOB-AUTHORITY","READ") %>
              <li class="nav-item">
                <a class="nav-link" id="" href="<%= positionjob_authority_level_path(lang: session[:lang]) %>" role="button">
                  <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-business-time"></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Cấp quyền hạn")  %></span>
                  </div>
                </a>
              </li>
              <% end %>
            </li>
          <% end %>
          <%if is_access(session["user_id"], "USERS","READ") || is_access(session["user_id"], "DISCIPLINE","READ") || is_access(session["user_id"], "RESOURCE","READ")  || is_access(session["user_id"], "PERMISSIONS","READ") || is_access(session["user_id"], "ORGANIZATION","READ") || is_access(session["user_id"], "RELIGIONS","READ") || is_access(session["user_id"], "EDUCATION","READ") || is_access(session["user_id"], "ACADEMICRANK","READ") || is_access(session["user_id"], "ETHNIC","READ") || is_access(session["user_id"], "NATIONALITY","READ") || is_access(session["user_id"], "USERTYPE","READ") || is_access(session["user_id"], "USERSTATUS","READ") || is_access(session["user_id"], "HOSPITALS","READ") || is_access(session["user_id"], "ARCHIVETYPES","READ")  || is_access(session["user_id"], "ARCHIVETYPES","READ") || is_access(session["user_id"], "ARCHIVELEVELS","READ") || is_access(session["user_id"], "DEPARTMENTTYPE","READ")  || is_access(session["user_id"], "DEPARTMENTTYPE","READ") || is_access(session["user_id"], "DEPARTMENTTYPES","READ") || is_access(session["user_id"], "CONTRACTTIME","READ") || is_access(session["user_id"], "CONTRACTTIME","READ") || is_access(session["user_id"], "CONTRACTTYPE","READ") || is_access(session["user_id"], "TMPCONTRACTS","READ") || is_access(session["user_id"], "BENEFITS","READ")  || is_access(session["user_id"], "BENEFITS","READ") || is_access(session["user_id"], "SBENEFITS","READ") || is_access(session["user_id"], "OPERSTREAM","READ") || is_access(session["user_id"], "OPERSTREAM","READ") || is_access(session["user_id"], "MHISTORIES","READ")  || is_access(session["user_id"], "MHISTORIES","READ") || is_access(session["user_id"], "ACCHISTS","READ") || is_access(session["user_id"], "FUNCTION","READ") || is_access(session["user_id"], "MAINTAIN","READ") || is_access(session["user_id"], "MAINTAIN","READ") %>
            <!-- Personnel -->
            <li class="nav-item">
              <!-- Lable Personnel -->
              <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
                <div class="col-auto navbar-vertical-label"><%= lib_translate("Personnel")  %>
                </div>
                <div class="col ps-0">
                  <hr class="mb-0 navbar-vertical-divider" />
                </div>
              </div>
              <!-- List of personnel -->
              <% if is_access(session["user_id"], "USERS","READ") %>
              <li class="nav-item">
                <a class="nav-link <%=  if ('/users/index' == request.env['PATH_INFO'])
                  'active'
                  elsif ('/user/details' == request.env['PATH_INFO'])
                  'active'
                  end %>"
                  id="users-index" href="<%= users_index_path(lang: session[:lang]) %>" role="button">
                  <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-users"></span></span><span class="nav-link-text ps-1"> <%= lib_translate("List_of_personnel")  %></span>
                  </div>
                </a>
              </li>
              <% end %>
              <!-- Personnel evaluation -->
              <% if is_access(session["user_id"], "DISCIPLINE","READ") %>
                <li class="nav-item">
                  <a class="nav-link dropdown-indicator" href="#Human_evaluation" role="button" data-bs-toggle="collapse" aria-expanded="false" aria-controls="Human_evaluation">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-user-check"></span></span><span class="nav-link-text ps-1"><%= lib_translate("Human_evaluation")  %></span>
                    </div>
                  </a>
                </li>
                <ul class="nav collapse <%= if ('/discipline/edit' == request.env['PATH_INFO'])
                    'show'
                    elsif ('/discipline/details' == request.env['PATH_INFO'])
                    'show' "active"
                  end %>" id="Human_evaluation">
                    <li class="nav-item">
                      <a class="nav-link <%= 'active' if '/discipline/edit' == request.env['PATH_INFO'] %>" href="<%= discipline_edit_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-user-shield" ></span></span><span class="nav-link-text ps-1"><%= lib_translate("Discipline")  %></span>
                        </div>
                      </a>
                    </li>
                </ul>
              <% end %>

              <!-- Config -->
              <% if is_access(session["user_id"], "RESOURCE","READ") || is_access(session["user_id"], "PERMISSIONS","READ") ||is_access(session["user_id"], "ORGANIZATION","READ") || is_access(session["user_id"], "RELIGIONS","READ") ||is_access(session["user_id"], "EDUCATION","READ") ||is_access(session["user_id"], "ACADEMICRANK","READ") ||is_access(session["user_id"], "ETHNIC","READ") ||is_access(session["user_id"], "NATIONALITY","READ") ||is_access(session["user_id"], "USERTYPE","READ") ||is_access(session["user_id"], "USERSTATUS","READ") ||is_access(session["user_id"], "HOSPITALS","READ") ||is_access(session["user_id"], "ARCHIVETYPES","READ") ||  is_access(session["user_id"], "ARCHIVELEVELS","READ") ||is_access(session["user_id"], "DEPARTMENTTYPE","READ") || is_access(session["user_id"], "DEPARTMENTTYPES","READ") ||is_access(session["user_id"], "CONTRACTTIME","READ") ||is_access(session["user_id"], "CONTRACTTYPE","READ") |is_access(session["user_id"], "TMPCONTRACTS","READ") ||is_access(session["user_id"], "BENEFITS","READ") || is_access(session["user_id"], "SBENEFITS","READ") ||is_access(session["user_id"], "OPERSTREAM","READ") ||is_access(session["user_id"], "MHISTORIES","READ") || is_access(session["user_id"], "ACCHISTS","READ") ||is_access(session["user_id"], "FUNCTION","READ") ||is_access(session["user_id"], "MAINTAIN","READ")   %>
                <li class="nav-item">
                  <a class="nav-link dropdown-indicator" href="#config" role="button" data-bs-toggle="collapse" aria-expanded="<%=
                  if ('/resource/edit' == request.env['PATH_INFO'])
                  'true'
                  else
                  'false'
                  end
                  %>" aria-controls="config">
                    <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-wrench"></span></span><span class="nav-link-text ps-1">   <%= lib_translate("Config")  %>   </span>
                    </div>
                  </a>
                </li>
                <ul class="nav collapse <%= if ('/resource/edit' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/permissions/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/organization/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/religions/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/education/edit' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/academicrank/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/ethnic/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/nationality/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/departmenttype/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/contracttime/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/contracttype/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/tbusertype/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/tbuserstatus/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/tbhospitals/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/tbarchivetypes/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/tbarchivelevels/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/tbdepartmenttypes/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/tbbenefits/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/sbenefits/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/tmpcontracts/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/operstream/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/mhistories/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/maintain/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/acchists/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/functions/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/documents/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/survey/index' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/survey/detail' == request.env['PATH_INFO'])
                  'show'
                  elsif ('/gsurveys/index' == request.env['PATH_INFO'])
                  'show'
                  
                  end %>" id="config">
                  <% if is_access(session["user_id"], "RESOURCE","READ") || is_access(session["user_id"], "PERMISSIONS","READ")%>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("Resource")%> & <%= lib_translate("Permissions")%>
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    <% if is_access(session["user_id"], "RESOURCE","READ") %>
                      <li class="nav-item"><a class="nav-link <%= 'active' if '/resource/edit' == request.env['PATH_INFO'] %>" href="<%= resource_edit_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-location-arrow" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Resource")  %></span>
                          </div>
                        </a>
                      </li>
                    <% end %>
                    <% if is_access(session["user_id"], "PERMISSIONS","READ") %>
                      <li class="nav-item"><a class="nav-link <%= 'active' if '/permissions/index' == request.env['PATH_INFO'] %>" href="<%= permissions_index_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-user-lock" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Permissions")  %></span>
                          </div>
                        </a>
                      </li>
                    <% end %>
                  <% end %>

                  <% if is_access(session["user_id"], "ORGANIZATION","READ") || is_access(session["user_id"], "RELIGIONS","READ") || is_access(session["user_id"], "EDUCATION","READ") || is_access(session["user_id"], "ACADEMICRANK","READ")|| is_access(session["user_id"], "ETHNIC","READ") || is_access(session["user_id"], "NATIONALITY","READ") || is_access(session["user_id"], "HOSPITALS","READ") || is_access(session["user_id"], "USERSTATUS","READ") || is_access(session["user_id"], "USERTYPE","READ")%>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("staff_information")  %>
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    <% if is_access(session["user_id"], "ORGANIZATION","READ") %>
                      <li class="nav-item"><a class="nav-link <%= 'active' if '/organization/index' == request.env['PATH_INFO'] %>" href="<%= organization_index_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-users-cog" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Organization")  %></span>
                          </div>
                        </a>
                      </li>
                    <% end %>
                    <% if is_access(session["user_id"], "RELIGIONS","READ") %>
                      <li class="nav-item"><a class="nav-link <%= 'active' if '/religions/index' == request.env['PATH_INFO'] %>" href="<%= religions_index_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-church" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Religion")  %></span>
                          </div>
                        </a>
                      </li>
                    <% end %>
                    <% if is_access(session["user_id"], "EDUCATION","READ") %>
                      <li class="nav-item"><a class="nav-link <%= 'active' if '/education/edit' == request.env['PATH_INFO'] %>" href="<%= education_edit_path(lang: session[:lang]) %>">
                          <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-user-graduate" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Education")  %></span>
                          </div>
                        </a>
                      </li>
                    <% end %>
                    <% if is_access(session["user_id"], "ACADEMICRANK","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/academicrank/index' == request.env['PATH_INFO'] %>" href="<%= academicrank_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-university" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Academic_rank")  %></span>
                        </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "ETHNIC","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/ethnic/index' == request.env['PATH_INFO'] %>" href="<%= ethnic_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-users" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Ethnic")  %></span>
                        </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "NATIONALITY","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/nationality/index' == request.env['PATH_INFO'] %>" href="<%= nationality_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-globe-asia" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Nationality")  %></span>
                        </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "USERTYPE","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/tbusertype/index' == request.env['PATH_INFO'] %>" href="<%= tbusertype_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-user-tag" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Tbusertype")  %></span>
                        </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "USERSTATUS","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/tbuserstatus/index' == request.env['PATH_INFO'] %>" href="<%= tbuserstatus_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-user-clock" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Tbuserstatus")  %></span>
                        </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "HOSPITALS","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/tbhospitals/index' == request.env['PATH_INFO'] %>" href="<%= tbhospitals_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-hospital-alt" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("tbhospitals")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                  <% end %>
                  <% if is_access(session["user_id"], "ARCHIVETYPES","READ") || is_access(session["user_id"], "ARCHIVELEVELS","READ") %>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("Archive")  %>
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    <% if is_access(session["user_id"], "ARCHIVETYPES","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/tbarchivetypes/index' == request.env['PATH_INFO'] %>" href="<%= tbarchivetypes_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-award" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Tbarchivetypes")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "ARCHIVELEVELS","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/tbarchivelevels/index' == request.env['PATH_INFO'] %>" href="<%= tbarchivelevels_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-ribbon" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Tbarchivelevels")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                  <% end %>
                    <% if is_access(session["user_id"], "DEPARTMENTTYPE","READ") || is_access(session["user_id"], "DEPARTMENTTYPES","READ")%>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("unit")  %>
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    <% if is_access(session["user_id"], "DEPARTMENTTYPE","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/departmenttype/index' == request.env['PATH_INFO'] %>" href="<%= departmenttype_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fab fa-buffer" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Department_type")  %></span>
                        </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "DEPARTMENTTYPES","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/tbdepartmenttypes/index' == request.env['PATH_INFO'] %>" href="<%= tbdepartmenttypes_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-bezier-curve" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Table_Type_of_Department")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                  <% end %>
                  <% if is_access(session["user_id"], "CONTRACTTIME","READ") || is_access(session["user_id"], "CONTRACTTYPE","READ") || is_access(session["user_id"], "TMPCONTRACTS","READ") %>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("Contract")  %>
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    <% if is_access(session["user_id"], "CONTRACTTIME","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/contracttime/index' == request.env['PATH_INFO'] %>" href="<%= contracttime_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-business-time" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Contract_time")  %></span>
                        </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "CONTRACTTYPE","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/contracttype/index' == request.env['PATH_INFO'] %>" href="<%= contracttype_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-copy" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Contract_type")  %></span>
                        </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "TMPCONTRACTS","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/tmpcontracts/index' == request.env['PATH_INFO'] %>" href="<%= tmpcontracts_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-file-alt" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Tmpcontracts")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                  <% end %>

                    <% if is_access(session["user_id"], "BENEFITS","READ") || is_access(session["user_id"], "SBENEFITS","READ")%>
                      <div class="row ms-2 mt-3 mb-2">
                        <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("Table_Tbbenefits")  %>
                        </div>
                        <div class="col ps-0">
                          <hr class="mb-0 navbar-vertical-divider" />
                        </div>
                      </div>
                      <% if is_access(session["user_id"], "BENEFITS","READ") %>
                      <li class="nav-item"><a class="nav-link <%= 'active' if '/tbbenefits/index' == request.env['PATH_INFO'] %>" href="<%= tbbenefits_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-hand-holding-usd" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Table_Tbbenefits")  %></span>
                        </div>
                        </a>
                      </li>
                      <% end %>
                      <% if is_access(session["user_id"], "SBENEFITS","READ") %>
                      <li class="nav-item"><a class="nav-link <%= 'active' if '/sbenefits/index' == request.env['PATH_INFO'] %>" href="<%= sbenefits_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-hands" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Sbenefits_menu")  %></span>
                        </div>
                        </a>
                      </li>
                      <% end %>
                    <% end %>

                    <% if is_access(session["user_id"], "OPERSTREAM","READ") %>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("Operstream")  %>
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    <% if is_access(session["user_id"], "OPERSTREAM","READ") %>
                      <li class="nav-item"><a class="nav-link <%= 'active' if '/operstream/index' == request.env['PATH_INFO'] %>" href="<%= operstream_index_path(lang: session[:lang]) %>">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-cogs"></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Operstream")  %></span>
                        </div>
                        </a>
                      </li>
                    <% end %>
                  <% end %>

                  <% if is_access(session["user_id"], "MHISTORIES","READ") || is_access(session["user_id"], "ACCHISTS","READ") ||is_access(session["user_id"], "FUNCTION","READ")%>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("Other")  %>
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    
                    <% if is_access(session["user_id"], "MHISTORIES","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/mhistories/index' == request.env['PATH_INFO'] %>" href="<%= mhistories_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-history" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Table_Histories")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "ACCHISTS","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/acchists/index' == request.env['PATH_INFO'] %>" href="<%= acchists_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-history" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Account_history")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                    <% if is_access(session["user_id"], "FUNCTION","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/functions/index' == request.env['PATH_INFO'] %>" href="<%= functions_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-cog" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("Function_set")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                    <%# if is_access(session["user_id"], "DOCUMENTS","READ") %>
                    <li class="nav-item">
                      <a class="nav-link <%= 'active' if '/documents/index' == request.env['PATH_INFO'] %>" href="<%= documents_index_path(lang: session[:lang]) %>" role="button">
                        <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-chart-pie"></span></span><span class="nav-link-text ps-1"><%= lib_translate("Documents")  %></span>
                        </div>
                      </a>
                    </li>
                    <%# end %>

                  <% end %>
                  <% if is_access(session["user_id"], "MAINTAIN","READ") %>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);"><%= lib_translate("MAINTAIN")  %>
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    <% if is_access(session["user_id"], "MAINTAIN","READ") %>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/maintain/index' == request.env['PATH_INFO'] %>" href="<%= maintain_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-hammer" ></span></span><span class="nav-link-text ps-1"> <%= lib_translate("MAINTAIN")  %></span>
                      </div>
                      </a>
                    </li>
                    <% end %>
                  <% end %>

                  <% if is_access(session["user_id"], "SURVEY","ADM")  %>
                    <div class="row ms-2 mt-3 mb-2">
                      <div class="col-auto navbar-vertical-label" style="color: var(--falcon-avatar-button-hover-bg);">Khảo sát
                      </div>
                      <div class="col ps-0">
                        <hr class="mb-0 navbar-vertical-divider" />
                      </div>
                    </div>
                    <li class="nav-item"><a class="nav-link <%= 'active' if request.env['PATH_INFO'] == '/survey/index' || request.env['PATH_INFO'] == '/survey/detail' %>" href="<%= survey_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-file" ></span></span><span class="nav-link-text ps-1"> Khảo sát</span>
                      </div>
                      </a>
                    </li>
                    <li class="nav-item"><a class="nav-link <%= 'active' if '/gsurveys/index' == request.env['PATH_INFO'] %>"  href="<%= gsurveys_index_path(lang: session[:lang]) %>">
                      <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-file-alt" ></span></span><span class="nav-link-text ps-1"> Nhóm khảo sát</span>
                      </div>
                      </a>
                    </li>
                  <% end %>

                </ul>
              <% end %>
            </li>
          <%end%>

          <% if is_access(session["user_id"], "DOCUMENTS-APP-ERP","READ") %>
            <li class="nav-item">
              <!-- Document -->
              <div class="row navbar-vertical-label-wrapper mt-3 mb-2">
                <div class="col-auto navbar-vertical-label"><%= lib_translate("Document")  %>
                </div>
                <div class="col ps-0">
                  <hr class="mb-0 navbar-vertical-divider" />
                </div>
              </div>
              <li class="nav-item">
                <a class="nav-link <%= 'active' if '/documents/erp' == request.env['PATH_INFO'] %>" href="<%= documents_erp_path(lang: session[:lang]) %>" role="button">
                  <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-book"></span></span><span class="nav-link-text ps-1"><%= lib_translate("Instruction_Manual")  %></span>
                  </div>
                </a>
              </li>
            </li>
          <%end%>

          <!--hidden menu -->
          <div class="row navbar-vertical-label-wrapper mt-3 mb-2 d-none">
            <div class="col-auto navbar-vertical-label"><%= lib_translate("Công cụ")  %></div>
            <div class="col ps-0">
              <hr class="mb-0 navbar-vertical-divider" />
            </div>
          </div>
          <li class="nav-item d-none">
            <a class="nav-link dropdown-indicator" href="#tools_programs" role="button" data-bs-toggle="collapse" aria-expanded="false" aria-controls="tools_programs">
              <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="fas fa-tools"></span></span><span class="nav-link-text ps-1"><%= lib_translate("Công cụ")  %></span></div>
            </a>
          </li>
          <ul class="nav collapse d-none <%= 'show' if '/tools/edit_word' == request.env['PATH_INFO'] || '/tools/spreadsheet' == request.env['PATH_INFO'] %>" id="tools_programs">
            <li class="nav-item">
              <a class="nav-link <%= 'active' if '/tools/edit_word' == request.env['PATH_INFO'] %>" href="<%= tools_edit_word_path(lang: session[:lang]) %>" role="button">
                <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-file-word fs-1"></span></span><span class="nav-link-text ps-1"><%= lib_translate("Trình soạn thảo")  %></span>
                </div>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <%= 'active' if '/tools/spreadsheet' == request.env['PATH_INFO'] %>" href="<%= tools_spreadsheet_path(lang: session[:lang]) %>" role="button">
                <div class="d-flex align-items-center"><span class="nav-link-icon"><span class="far fa-file-excel fs-1"></span></span><span class="nav-link-text ps-1"><%= lib_translate("Bảng tính")  %></span>
                </div>
              </a>
            </li>
          </ul>
          <!-- end hidden menu -->
        </ul>
      </div>
    </div>
  </nav>