class Department < ApplicationRecord
  # has_many :works, :dependent => :delete_all
  has_many :works, through: :positionjobs
  # has_many :responsibles, :dependent => :destroy
  has_many :positionjobs, :dependent => :destroy
  has_many :nodes, :dependent => :destroy
  has_many :ddocs, :dependent => :destroy
  has_many :node
  # has_many :positionjobs
  has_many :ddocs, class_name: "Ddoc"
  has_many :mediafiles, through: :ddocs

  # 08/05/2025
  belongs_to :organization

  belongs_to :leader_user, class_name: 'User', foreign_key: 'leader', optional: true
  belongs_to :deputy_user, class_name: 'User', foreign_key: 'deputy', optional: true
  
  # <PERSON><PERSON><PERSON>en - 2025-06-30
  def is_subdepartment?
    parents.present?
  end

  # Validations chỉ áp dụng cho subdepartment
  validates :name, presence: { message: "Tên nhóm tiếng Việt không được để trống" }, if: :is_subdepartment?
  validates :name, length: { maximum: 255, message: "Tên nhóm tiếng Việt không được vượt quá 255 ký tự" }, if: :is_subdepartment?
  validates :leader, presence: { message: "Trưởng nhóm không được để trống" }, if: :is_subdepartment?
  validates :deputy, presence: { message: "Phó nhóm không được để trống" }, if: :is_subdepartment?
  validates :name_en, presence: { message: "Tên nhóm tiếng Anh không được để trống" }, if: :is_subdepartment?
  validates :name_en, length: { maximum: 255, message: "Tên nhóm tiếng Anh không được vượt quá 255 ký tự" }, if: :is_subdepartment?
  validates :amount, presence: { message: "Số lượng không được để trống" }, if: :is_subdepartment?
  validate :validate_subdepartment_rules, if: :is_subdepartment?

  def leader_name
    leader_user&.full_name || 'Chưa có trưởng nhóm'
  end
  
  def deputy_name
    deputy_user&.full_name || 'Chưa có phó nhóm'
  end

  private

  def validate_subdepartment_rules
    return unless is_subdepartment?
    
    # Kiểm tra leader và deputy khác nhau
    if leader.present? && deputy.present? && leader == deputy
      errors.add(:deputy, "Phó nhóm không thể trùng với trưởng nhóm")
    end

    if amount.present?
      if amount.match?(/^\d+$/)
        if amount.to_i <= 0
          errors.add(:amount, "Số lượng phải lớn hơn 0")
        elsif amount.to_i > 999
          errors.add(:amount, "Số lượng không được vượt quá 999")
        end
      else
        errors.add(:amount, "Số lượng phải là số nguyên")
      end
    end
  end
end
