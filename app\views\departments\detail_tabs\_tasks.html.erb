<style>
    .accordion-button::after{
        margin: unset !important;
    }
</style>
<div class="mt-1 d-flex justify-content-between">
    <div class="" style="width: fit-content;">
        <%= form_tag departments_department_details_path(department_id: params[:department_id],tab: @tab_names[:tasks], lang: session[:lang]), method: :post do %>
            <div class="input-group">
                <%= text_field_tag :search, params[:search], class: "form-control", id: "function_search", placeholder: "Tìm kiếm" %>
                <span class="input-group-text bg-white"><span class="fas fa-search" style="color:#999999"></span></span>
            </div>
        <% end %>
    </div>
    <div class="d-flex justify-content-between d-none">
        <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#select-tfunction-modal">
            <span class="fas fa-plus me-2"></span>
            <span>Thêm chức năng</span>
        </button>
    </div>
</div>
<div class="mt-2">
    <!-- Danh sách chức năng/nhiệm vụ - công việc khác -->
    <table id="table_functions" class="table table-customs table-bordered fs--1 mb-0" style="margin-top: 15px !important; margin-bottom: 0px !important">
        <thead>
            <tr style="background-color: #EFF2F1;">
                <th class="no-sort" style="font-size: 16px; width: 100%;">Chức năng</th>
            </tr>
        </thead>
        <tbody class="list">
            <% if @functions.any? %>
                <tr>
                    <td class="p-0">
                        <!-- Accordion cho danh sách chức năng -->
                        <div class="accordion accordion-left-icon" id="functionAccordion">
                        <% @functions.each_with_index do |function, index| %>
                            <div class="accordion-item border-0">
                            <span class="accordion-header" id="heading-<%= function.id %>">
                                <button class="accordion-button" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#function-collapse-<%= function.id %>" 
                                    aria-expanded="true" aria-controls="function-collapse-<%= function.id %>"
                                    style="display: flex;flex-direction: row-reverse;justify-content: flex-end;">
                                    <span class="count_dueties ms-2">(<%= Tfunction.where(id: @data_stasks.pluck(:tfunction_id), parent: function.id).count %>)</span>
                                    <span class="ms-2" style="font-size: 16px; color: #333333;"><%= function.name %></span>
                                </button>
                            </span>
                            <div id="function-collapse-<%= function.id %>" class="accordion-collapse collapse show" 
                                aria-labelledby="heading-<%= function.id %>">
                                <div class="accordion-body">
                                <% if function.get_duties.any? %>
                                    <!-- Accordion cho nhiệm vụ của chức năng này -->
                                    <div class="accordion accordion-left-icon ms-3" id="dutyAccordion-<%= function.id %>">
                                    <% function.get_duties.each_with_index do |duty, duty_index|
                                        related_stasks = @data_stasks.select { |s| s.tfunction_id == duty.id }
                                    %>
                                        <% if !related_stasks.empty? %>
                                            <div class="accordion-item border-0 mb-2">
                                                <span class="accordion-header" id="duty-heading-<%= duty.id %>">
                                                    <button class="accordion-button p-2" type="button" 
                                                            data-bs-toggle="collapse" data-bs-target="#duty-collapse-<%= duty.id %>" 
                                                            aria-expanded="true" aria-controls="duty-collapse-<%= duty.id %>"
                                                            style="display: flex;flex-direction: row-reverse;justify-content: flex-end;">
                                                        <span class="ms-2"><%= duty.name %></span>
                                                    </button>
                                                </span>
                                                <div id="duty-collapse-<%= duty.id %>" class="accordion-collapse collapse show" 
                                                    aria-labelledby="duty-heading-<%= duty.id %>">
                                                    <div class="accordion-body" style="background-color: #ECECEC; border-radius: 10px;">
                                                        <!-- Bảng công việc thuộc nhiệm vụ này -->
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered">
                                                                <thead style="background-color: #F9FBFC;">
                                                                    <tr>
                                                                        <th style="width: 5%;">#</th>
                                                                        <th style="width: 20%;">Tên công việc</th>
                                                                        <th style="width: 10%;">Mức độ TX</th>
                                                                        <th style="width: 10%;">Cấp xử lý</th>
                                                                        <th style="width: 10%;">Mức độ khó</th>
                                                                        <th style="width: 10%;">Mức độ ưu tiên</th>
                                                                        <th style="width: 20%;">Quy trình</th>
                                                                        <th style="width: 15%;">Ghi chú</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody style="background-color: white;">
                                                                    <% tasks = Stask.where(id: @data_stasks.pluck(:id), tfunction_id: duty.id) %>
                                                                    <% if tasks.any? %>
                                                                    <% tasks.each_with_index do |task, index| %>
                                                                        <tr>
                                                                            <td><%= index + 1 %></td>
                                                                            <td><%= task.name %></td>
                                                                            <td>
                                                                                <% if task.frequency.present? %>
                                                                                    <%= WorksHelper::FREQUENCY_TYPES[task.frequency.to_sym] || task.frequency %>
                                                                                <% else %>
                                                                                    <%= "-" %>
                                                                                <% end %>
                                                                            </td>
                                                                            <td><%= task.level_handling %></td>
                                                                            <td><%= task.level_difficulty %></td>
                                                                            <td><%= task.priority %></td>
                                                                            <td class="text-start">
                                                                                <% if task.files.present? %>
                                                                                    <ul class="list-unstyled mb-0">
                                                                                        <% task.files.each do |file| %>
                                                                                            <div style="display: flex; align-items: center;">
                                                                                                <img class="task-icon" src="<%= image_path("/assets/image/#{file[:file_icon]}.png") %>">
                                                                                                <li style="list-style: none;"><%= file[:file_name] %></li>
                                                                                            </div>
                                                                                        <% end %>
                                                                                    </ul>
                                                                                <% else %>
                                                                                    <span class="text-muted">Không có file</span>
                                                                                <% end %>
                                                                            </td>
                                                                            <td><%= task.note %></td>
                                                                        </tr>
                                                                    <% end %>
                                                                    <% else %>
                                                                    <tr>
                                                                        <td colspan="8" class="text-center">Không có công việc nào</td>
                                                                    </tr>
                                                                    <% end %>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <% end %>
                                    <% end %>
                                    </div>
                                <% else %>
                                    <div class="text-center py-2" style="background-color: #ECECEC; border-radius: 10px;">Không có nhiệm vụ nào</div>
                                <% end %>
                                </div>
                            </div>
                            </div>
                        <% end %>
                        </div>
                    </td>
                </tr>
            <% else %>
                <tr>
                    <td class="text-center">Không có chức năng</td>
                </tr>
            <% end %>
        </tbody>
    </table>

    <!-- Danh sách công việc khác -->
    <table id="" class="table table-customs table-bordered fs--1 mb-0" style="margin-top: 15px !important; margin-bottom: 0px !important">
        <thead>
            <tr style="background-color: #EFF2F1;">
                <th class="no-sort" style="font-size: 16px; width: 100%;">Công việc khác</th>
            </tr>
        </thead>
        <tbody class="list">
            <% if @stasks_without_duty.any?

            %>
                <tr>
                    <td class="p-0">
                        <!-- Bảng công việc khác -->
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead style="background-color: #F9FBFC;">
                                    <tr>
                                        <th style="width: 5%;">#</th>
                                        <th style="width: 20%;">Tên công việc</th>
                                        <th style="width: 10%;">Mức độ TX</th>
                                        <th style="width: 10%;">Cấp xử lý</th>
                                        <th style="width: 10%;">Mức độ khó</th>
                                        <th style="width: 10%;">Mức độ ưu tiên</th>
                                        <th style="width: 20%;">Quy trình</th>
                                        <th style="width: 15%;">Ghi chú</th>
                                    </tr>
                                </thead>
                                <tbody style="background-color: white;">
                                    <% @stasks_without_duty.each_with_index do |task, index| %>
                                        <tr>
                                            <td><%= index + 1 %></td>
                                            <td><%= task.name %></td>
                                            <td>
                                                <% if task.frequency.present? %>
                                                    <%= WorksHelper::FREQUENCY_TYPES[task.frequency.to_sym] || task.frequency %>
                                                <% else %>
                                                    <%= "-" %>
                                                <% end %>
                                            </td>
                                            <td><%= task.level_handling %></td>
                                            <td><%= task.level_difficulty %></td>
                                            <td><%= task.priority %></td>
                                            <td class="text-start">
                                                <% if task.files.present? %>
                                                    <ul class="list-unstyled mb-0">
                                                        <% task.files.each do |file| %>
                                                            <div style="display: flex; align-items: center;">
                                                                <img class="task-icon" src="<%= image_path("/assets/image/#{file[:file_icon]}.png") %>">
                                                                <li style="list-style: none;"><%= file[:file_name] %></li>
                                                            </div>
                                                        <% end %>
                                                    </ul>
                                                <% else %>
                                                    <span class="text-muted">Không có file</span>
                                                <% end %>
                                            </td>
                                            <td><%= task.note %></td>
                                        </tr>
                                    <% end %>
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            <% else %>
                <tr>
                    <td class="text-center">Không có công việc khác</td>
                </tr>
            <% end %>
        </tbody>
    </table>

    
    <div class='d-flex justify-content-between align-items-center mt-3'>
        <div style="font-size: 14px; font-weight: 400; color: #4E606E;">
            <%=lib_translate('Show')%> <%= @functions.size %> <%=lib_translate('Up_to')%> <%= @total_records %> <%=lib_translate('Record')%>
        </div>
        <div>
            <%= render_pagination_limit_offset(departments_department_details_path(department_id: params[:department_id],tab: @tab_names[:tasks], lang: session[:lang], per_page: session[:per_page], search: session[:search]), 10, @functions.count).html_safe %>
        </div>
    </div>
</div>
<%= form_tag add_function_into_department_path(department_id: params[:department_id]), method: :post, class: "", id: "form-update-works", authenticity_token: true do %>
    <div class="modal fade" id="select-tfunction-modal" data-bs-keyboard="false" data-bs-backdrop="static" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="">Chọn chức năng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row gx-0">
                        <label for="">Chức năng</label>
                        <select name="function_id" id="">
                            <% @tfunctions.each do |f| %>
                                <option value="<%= f.id %>"><%= f.name %></option>
                            <% end %>
                        </select>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Đóng</button>
                    <button class="btn btn-primary" type="submit">Thêm</button>
                </div>
            </div>
        </div>
    </div>
<% end %>