/* Styles cho Department List với Subdepartments */

/* Animation cho collapse/expand */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Subdepartment rows animation */
.subdepartment-row {
    animation: slideDown 0.3s ease-out;
    border-left: 3px solid #e3f2fd !important;
    position: relative;
}

.subdepartment-row::before {
    content: '';
    position: absolute;
    left: -3px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #2196f3, #64b5f6);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.subdepartment-row:hover::before {
    opacity: 1;
}

/* Parent department row hover effects */
.department-parent-row {
    transition: all 0.2s ease;
    position: relative;
}

.department-parent-row:hover {
    background-color: #f8f9fa !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Collapse toggle button improvements */
.collapse-toggle {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    position: relative;
}

.collapse-toggle:hover {
    background-color: #e3f2fd !important;
    color: #1976d2 !important;
    transform: scale(1.1);
}

.collapse-toggle:active {
    transform: scale(0.95);
}

/* Chevron icon improvements */
.collapse-toggle i {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 12px;
}

/* Badge improvements */
.badge {
    font-size: 0.75em;
    font-weight: 500;
    padding: 0.25em 0.5em;
    border-radius: 0.375rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge.bg-light {
    background-color: #f1f3f4 !important;
    color: #5f6368 !important;
    border: 1px solid #dadce0;
}

.badge.bg-info {
    background: linear-gradient(45deg, #17a2b8, #20c997) !important;
    border: none;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.badge.bg-secondary {
    background: linear-gradient(45deg, #6c757d, #495057) !important;
    border: none;
}

/* Subdepartment content styling */
.subdepartment-row .small {
    line-height: 1.3;
}

.subdepartment-row .small div {
    margin-bottom: 2px;
}

.subdepartment-row .small div:last-child {
    margin-bottom: 0;
}

/* Arrow icon for subdepartments */
.fa-arrow-turn-down-right {
    color: #6c757d;
    opacity: 0.7;
}

/* Table improvements */
#table-deparments {
    border-collapse: separate;
    border-spacing: 0;
}

#table-deparments thead th {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #E7EDF0;
    border-bottom: 2px solid #dee2e6;
}

#table-deparments tbody tr {
    border-bottom: 1px solid #dee2e6;
}

#table-deparments tbody tr:last-child {
    border-bottom: none;
}

/* Loading state */
.loading-subdepartments {
    opacity: 0.6;
    pointer-events: none;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .subdepartment-row .small {
        font-size: 11px !important;
    }
    
    .collapse-toggle {
        width: 20px;
        height: 20px;
        margin-right: 8px !important;
    }
    
    .collapse-toggle i {
        font-size: 10px;
    }
    
    .badge {
        font-size: 0.65em;
        padding: 0.2em 0.4em;
    }
    
    .subdepartment-row {
        border-left-width: 2px !important;
    }
}

@media (max-width: 576px) {
    .department-parent-row .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .collapse-toggle {
        margin-bottom: 4px;
    }
    
    .badge {
        margin-top: 4px;
        margin-left: 0 !important;
    }
}

/* Print styles */
@media print {
    .collapse-toggle {
        display: none !important;
    }
    
    .subdepartment-row {
        display: table-row !important;
        background-color: #f9f9f9 !important;
    }
    
    .badge {
        background-color: #6c757d !important;
        color: white !important;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .department-parent-row:hover {
        background-color: #2d3748 !important;
    }
    
    .subdepartment-row {
        background-color: #1a202c !important;
        border-left-color: #4299e1 !important;
    }
    
    .subdepartment-row:hover {
        background-color: #2d3748 !important;
    }
    
    .collapse-toggle:hover {
        background-color: #2d3748 !important;
        color: #4299e1 !important;
    }
}

/* Accessibility improvements */
.collapse-toggle:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

.subdepartment-row:focus-within {
    background-color: #e3f2fd !important;
}

/* Status badge improvements */
.stastus-badge {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Smooth transitions for all interactive elements */
* {
    transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
}
