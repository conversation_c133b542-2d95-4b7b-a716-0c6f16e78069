<style>
    table td {
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: clip !important;
    }
</style>

<div class="page-list-positionjob">
    <div class="mt-1 d-flex">
        <div class="me-auto">
            <%= form_tag departments_department_details_path(tab: @tab_names[:subdepartments], lang: session[:lang], department_id: params[:department_id]), method: :post do %>
                <div class="input-group">
                    <%= text_field_tag :search, params[:search], class: "form-control", id: "function_search", placeholder: "Tìm kiếm" %>
                    <span class="input-group-text bg-white">
                        <span class="fas fa-search" style="color:#999999"></span>
                    </span>
                </div>
            <% end %>
        </div>
        <%= link_to new_subdepartment_path(department_id: params[:department_id]), class: "btn btn-primary me-3", id: "btn-add-subdepartment", onclick: "showLoadding(true);", remote: true do %>
            <i class="fas fa-plus"></i> Thêm nhóm
        <% end %>
    </div>

    <div class="mt-2">
        <table class="table" style="background:white; margin-top: 15px;">
            <thead>
                <tr style="background: #F9FBFC;height: 38px;vertical-align: middle;">
                    <th scope="col" style="width: 5%; text-align: center;">#</th>
                    <th scope="col" style="width: 25%;">Tên nhóm</th>
                    <th scope="col" style="width: 10%;">Số lượng</th>
                    <th scope="col" style="width: 25%;">Trưởng nhóm</th>
                    <th scope="col" style="width: 25%;">Mô tả</th>
                    <th scope="col" style="width: 10%; text-align: center;">Thao tác</th>
                </tr>
            </thead>
            <tbody>
                <% if @subdepartments.any? %>
                    <% @subdepartments.each_with_index do |s, index| %>
                        <tr id="function-<%= s.id %>">
                            <td><%= (session[:page].to_i - 1) * session[:per_page].to_i + index + 1 %></td>
                            <td class="text-start"><%= s.name %></td>
                            <td class="text-start"><%= s.amount %></td>
                            <td class="text-start"><%= s.leader_name %></td>
                            <td class="text-start"><%= s.note %></td>
                            <td class="text-center">
                                <div class="d-flex justify-content-center align-items-center">
                                    <%= link_to assign_users_subdepartment_path(s), class: 'btn btn-link', onclick: "showLoadding(true);",
                                                data: { bs_toggle: 'tooltip', bs_placement: 'top', bs_title: 'Gán nhân sự' },
                                                remote: true do %>
                                        <span class='fas fa-users text-success fs-2'></span>
                                    <% end %>

                                    <%= link_to edit_subdepartment_path(s, department_id: params[:department_id]), class: 'btn btn-link', onclick: "showLoadding(true);",
                                                data: { bs_toggle: 'tooltip', bs_placement: 'top', bs_title: 'Chỉnh sửa' },
                                                remote: true do %>
                                        <i class="text-primary fas fa-pen fs-2"></i>
                                    <% end %>

                                    <%= link_to subdepartment_path(s, department_id: params[:department_id]), class: 'btn btn-link delete-subdepartment-btn', 
                                                method: :delete,
                                                data: { 
                                                    bs_toggle: 'tooltip', 
                                                    bs_placement: 'top', 
                                                    bs_title: 'Xóa',
                                                    subdepartment_name: s.name,
                                                    subdepartment_id: s.id,
                                                    remote: true,
                                                    confirm: "Bạn có chắc chắn muốn xóa nhóm \"#{s.name}\" không?\nHành động này không thể hoàn tác!"
                                                } do %>
                                        <i class="fas fa-trash-alt text-danger fs-2"></i>
                                    <% end %>
                                </div>
                            </td>
                        </tr>
                    <% end %>
                <% else %>
                    <tr>
                        <td colspan="4" class="text-center">Không có dữ liệu</td>
                    </tr>
                <% end %>
            </tbody>
        </table>

        <div class='d-flex justify-content-between align-items-center mt-3'>
            <div style="font-size: 14px; font-weight: 400; color: #4E606E;">
                <%
                    current_page = session[:page]&.to_i || 1
                    per_page = session[:per_page]&.to_i || 10
                    start_record = (current_page - 1) * per_page + 1
                    end_record = [current_page * per_page, @total_records].min
                %>

                <%=lib_translate('Show')%> <%= start_record %>-<%= end_record %> <%=lib_translate('Up_to')%> <%= @total_records %> <%=lib_translate('Record')%>
            </div>
            <div>
                <%= render_pagination_limit_offset(departments_department_details_path(tab: @tab_names[:subdepartments], lang: session[:lang], department_id: params[:department_id], per_page: session[:per_page], search: session[:search]), 10, @subdepartments.count).html_safe %>
            </div>
        </div>
    </div>
</div>