<% content_for :head do %>
  <%= javascript_include_tag '/assets/myjs/choices.js' %>
  <%= stylesheet_link_tag '/assets/mystyle/choices.min.css'%>
  <%= stylesheet_link_tag "select2-bootstrap-5-theme.min" %>
  <%= stylesheet_link_tag "select2.min" %>
  <%= stylesheet_link_tag 'jquery-ui.css' %>  
<% end %>
<% content_for :bottombody do %>
    <%= javascript_include_tag 'select2.full.min'%>
    <%= javascript_include_tag 'select2.min.js'%>
    <%= javascript_include_tag '/assets/lib_hrm/bootstrap.min.js', integrity: 'sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl', crossorigin: 'anonymous'%>
    <script>
        const selects = [
            "select[name='positionjob_id']", 
            "select[name='users_id[]']",
            "select[name='function_id']",
            
        ];
        selects.forEach(select => {
            const selectElement = $(select);
            if (selectElement.length) {
                if (selectElement.hasClass("select2-hidden-accessible")) {
                    selectElement.select2('destroy');
                }
                selectElement.select2({
                    width: '100%',
                    theme: "bootstrap-5",
                    placeholder: selectElement.attr("placeholder"),
                    closeOnSelect: false
                });
            }
        });
    </script>
<% end %>
<style>
    #tab-list li a.active{
        color: #2C7BE5 !important;
        font-weight: bold !important;
    }
    .r-count{
        border: 1px solid #d3d3d3;
        padding: 0px 7px 0px 7px;
        border-radius: 30px;
        text-align: center;
        font-weight: bold;
    }

    /* Tab navigation styling */
    .nav-tabs-custom {
        position: relative;
    }
    
    .nav-tabs-custom .nav-item {
        margin-bottom: 0;
    }
    
    .nav-tabs-custom .nav-link {
        background: transparent;
        border: none;
        border-radius: 0;
        font-weight: 500;
        font-size: 15px;
        position: relative;
        transition: all 0.3s ease;
        text-decoration: none;
        border-bottom: 3px solid transparent;
    }
    
    .nav-tabs-custom .nav-link:hover {
        background: rgba(44, 123, 229, 0.08);
        color: #2C7BE5;
        border-color: transparent;
    }
    
    .nav-tabs-custom .nav-link.active {
        background: #ffffff;
        color: #2C7BE5 !important;
        font-weight: 700 !important;
        border-bottom: 3px solid #2C7BE5;
        position: relative;
    }
    
    .nav-tabs-custom .nav-link.active::before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background: #ffffff;
        z-index: 1;
    }
</style>
<div class="mb-2">
    <a href="<%= departments_department_list_path()%>">Danh sách đơn vị</a>
    <span class="fas fa-angle-right ms-2 me-2" style="color: #919191;"></span>
    <span style="font-weight: 700;"><%= @oDepartment.name %></span>
</div>
<div class="card" style="flex-grow: 1;">
    <div class="card-body p-3">
        <h4 class="mb-3"><%= @oDepartment.name %></h4>
        <div class="card-body position-relative">
            <ul class="nav nav-tabs nav-tabs-custom" id="tab-list" style="border-bottom: none;">
                <% @tab_names.each do |tab,name| %>
                    <% next if @oDepartment.status == '1' && tab != :info %>
                    <li class="nav-item pe-2">
                        <%= link_to departments_department_details_path(tab: name, lang: session[:lang], department_id: params[:department_id]),
                                    class: "nav-link #{@current_tab == name ? 'active' : ''}",
                                    style: "font-size: 18px; font-weight: 400;",
                                    role: "tab" do %>
                                    <%= name %>
                        <% end %>
                    </li>
                <%end%>
            </ul>
        </div>
        <div class="tab-content">
            <%= render partial: "departments/detail_tabs/info" if @current_tab == @tab_names[:info] %>
            <%if @oDepartment.status == '0'%>
                <%= render partial: "departments/detail_tabs/tasks" if @current_tab == @tab_names[:tasks] %>
                <%= render partial: "departments/detail_tabs/positionjobs" if @current_tab == @tab_names[:positionjobs] %>
                <%= render partial: "departments/detail_tabs/users" if @current_tab == @tab_names[:users] %>
                <%= render partial: "departments/detail_tabs/subdepartments" if @current_tab == @tab_names[:subdepartments] %>
                <%= render partial: "departments/detail_tabs/report" if @current_tab == @tab_names[:report] %>
                <%= render partial: "departments/detail_tabs/kpi" if @current_tab == @tab_names[:kpi] %>
                <%= render partial: "departments/detail_tabs/rewards" if @current_tab == @tab_names[:rewards] %>
            <%end%>
        </div>
    </div>
</div>