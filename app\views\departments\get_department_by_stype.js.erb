<%
    rows = []
    status_list = [
                    {status:"0",text:"Hoạt động",style:"success"},
                    {status:"1",text:"Dừng hoạt động",style:"secondary"},
                    {status:"",text:"Đình chỉ",style:"danger"},
                    {status:"",text:"Cần điều động",style:"warning"},
                    {status:"",text:"Giải thể",style:"secondary"},
                    {status:"",text:"Cần tuyển",style:"yellow"}
                ]
    @departments.each_with_index do |department,index|
        status = status_list.select{|item| item[:status] == department.status}.first
        stt = (index + 1) + ((session[:page].to_i - 1) * session[:per_page].to_i)
        rows << "<tr style='vertical-align: middle;cursor: pointer;' onclick='clickDepartmentRow(#{department.id})'>
                    <td>#{stt}</td>
                    <td class='pt-2 pb-2'>
                        <div>
                            <p class='p-0 m-0' style='font-weight: 500;color: #454545;'>#{department.name}</p>
                            <p class='p-0 m-0' style='font-weight: 400;font-size: 14px;color: #767676;font-style: italic;line-height: 10px;'>#{department.name_en}</p>
                        </div>
                    </td>
                    <td>#{department.email}</td>
                    <td>#{department.last_name} #{department.first_name}</td>
                    <td>#{department.office}</td>
                    <td style='text-align: center;'>#{department.org_name}</td>
                    <td style='text-align: center;'>
                        <span class='stastus-badge rounded-pill badge-soft-#{status[:style]}'>#{status[:text]}</span>
                    </td>
                    <td style='text-align: center;'>#{department.user_count}</td>
                </tr>"
    end

%>
var tbody = $("#table-deparments").find("tbody");
tbody.html("");
tbody.append(`<%= rows.join("").html_safe%>`);

$(".per-page-wrap").show();
$(".per_page").html(<%= session[:per_page]%>);
$(".item_count").html(<%= @departments.size%>);
$(".total_record").html(<%= @total_records%>);
var paginWrap = $(".pagin-wrap");
paginWrap.html(`<%= render_pagination_style_1("clickButtonPage",session[:page],session[:total_pages], session[:per_page]).html_safe%>`);

showLoadding(false);