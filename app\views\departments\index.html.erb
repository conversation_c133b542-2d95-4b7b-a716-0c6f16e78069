
<% content_for :head do %>
  <%= javascript_include_tag '/assets/lib_hrm/formmedia_old.js' %>
  <%= stylesheet_link_tag '/assets/lib_hrm/formmedia.css' %>
  <%= stylesheet_link_tag "select2-bootstrap-5-theme.min" %>
  <%= stylesheet_link_tag "select2.min" %>
  <%= stylesheet_link_tag 'jquery-ui.css' %>
  <%= stylesheet_link_tag '/assets/lib_hrm/font-open-sans.css' %>
  <%= stylesheet_link_tag '/assets/mystyle/department.css' %>
<% end %>

<%= javascript_include_tag 'jquery-ui.js'%>
<% content_for :bottombody do %>
    <script>
      //datepicker
        $(function () {
            $( ".datepicker" ).datetimepicker({
              format: 'DD/MM/YYYY'
            });
      });
    </script>
    <%= javascript_include_tag '/assets/myjs/department.js' %>
    <%= javascript_include_tag 'select2.full.min'%>
    <%= javascript_include_tag 'select2.min.js'%>
    <%= javascript_include_tag '/assets/lib_hrm/bootstrap.min.js', integrity: 'sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl', crossorigin: 'anonymous'%>
    <%= javascript_include_tag '/assets/lib_hrm/moment-with-locales.js' %>
    <%= javascript_include_tag '/assets/lib_hrm/bootstrap-datetimepicker.min.js' %>
<% end %>
<div class="card mb-3">
  <div class="card-body pt-0 ">
    <div class="tab-content">
      <div class="row">
        <div class="col-12 col-sm-12 col-md-12 col-lg-8 col-xl-8 col-xxl-8 tag d-flex align-items-center title mt-3">
            <h4 class="mb-1" style="color: var(--falcon-btn-falcon-success-active-color);"><%= lib_translate('List_Department')%></h4>
        </div>
      <% if is_access(session["user_id"], "DEPARTMENTS","ADD") %> 
          <div class="col-12 col-sm-12 col-md-12 col-lg-4 col-xl-4 col-xxl-4 action-right-top d-flex align-items-end justify-content-end">
              <a id="btn_add_department" class="nav-link ps-2 pe-3 border-end border-2 mb-1" role="button" style="font-size: 13px; font-weight: 700" onclick="openFormAddDepartment()" class="btn btn-primary mt-3"  data-bs-toggle="collapse" data-bs-target="#form_department" aria-expanded="false" aria-controls="form_department" >
                <%= lib_translate("Add_new_Department") %>
              </a>
              <a id="id_cancel_department" class=" nav-link ps-2 pe-3 border-end border-2"  style="display:none; font-size: 13px; font-weight: 700;cursor:pointer" data-bs-toggle="collapse" data-bs-target="#form_department" aria-expanded="false" aria-controls="form_department">
                <%= lib_translate("Cancle") %>
              </a>
          </div>
        </div>
        <div class=" collapse capp-form-bg " id="form_department" >
          <div class="card capp-form-bg" >
            <div class="card-body rounded">
                <div class="tab-content">
                  <div class="table-responsive scrollbar">
                    <%= form_for @department, url: department_update_path, html: {method: :post, id:"form_add_department" } do |f| %>
                      <p id="cls_bmtu_form_add_title"  class=" mt-3 fw-bold fs-3 d-flex justify-content-center">
                        <%= lib_translate("Add_new_Department")  %>
                      </p>
                      <div class="erro_labble"style="height: 10px; "  >
                        <p style="color:red;height: 10px" id="erro_labble_content"></p>
                      </div>
                      <br>
                      <div class="form-group" style="display:none;">
                        <%= f.text_field :id, class: 'form-control', name: 'department_id', id:"department_id" %>
                      </div>
                      <div class="form-group" style="display:none;" >
                        <input value="1" type="text" id="type_upadte_department" name="type_upadte_department"><br>
                      </div>
                        <div class="row group-form-department-1" >
                          <div class=" col-4 d-flex flex-column field-group-department-name">
                            <div class="  form-group ">
                              <label> <%= lib_translate("Host_unit")%> <span class="red">* </span><a class="ms-2" href="<%= organization_index_path() %>"><span style="width:10px; color:#98a7b9" class="fas fa-database"></span></a></label>
                              <select class="form-select" name="organization_id" id="organization_id">
                                <option value="" selected>Chọn đơn vị chủ quản</option>
                                <% @organizations.each do |organization| %>
                                  <option data-scode="<%= organization.scode%>"  value="<%=organization.id%>" <%= 'selected' if @user_organization_id == organization.id%> ><%= organization.name%></option>
                                <%end%>
                              </select>
                            </div>
                            <div class="form-group  ">
                              <label> <%= lib_translate("Department_type")%> <span class="red">* <a class="ms-2" href="<%= tbdepartmenttypes_index_path() %>"><span style="width:10px; color:#98a7b9" class="fas fa-database"></span></a> </span></label>
                                <select class="form-select" name="sel_department_type" id="sel_department_type">
                                  <% @tbdepartmenttypes.each do |tbdepartmenttype| %>
                                    <option value="<%=tbdepartmenttype.id%>"><%=tbdepartmenttype.name%> </option>
                                  <%end%>
                                </select>
                            </div>
                                <%# thai 22/12/2022 %>
                            <div class="form-group" id="issue_id_department">
                              <label> <%= lib_translate("Decide_to_establish")  %>  </label>
                              <%= f.text_field :issue_id, class: 'form-control', name: 'txt_issue_id_department', id:"txt_issue_id_department" %>
                            </div>
                            <div class="form-group mb-2" id="name_en_department">
                              <label  > <%= lib_translate("Issued_by_department")  %>  </label>
                              <select class="form-select selectpicker" name="txt_issued_by" id="txt_issued_by">
                                <% @users.each do |user| %>
                                    <option value="<%= user.email %>">
                                        <%= user.last_name + " " + user.first_name %>
                                          (<%= user.email%>)
                                    </option>
                                    <%end%>
                              </select>
                            </div>
                            <div class="form-group mb-2" id="name_en_department_parent">
                              <label  > <%= lib_translate("Đơn vị cha")  %>  </label>
                              <select class="form-select selectpicker" name="department_parent" id="department_parent">
                              </select>
                            </div>
                          </div>
                          <div class=" col-4 d-flex flex-column field-group-department-name">
                            <div class="form-group" id="name_department">
                              <label  > <%= lib_translate("Department_name")  %>  <span style="color:red;" class="red">* </span></label>
                              <%= f.text_field :name, class: 'form-control', name: 'txt_name_department',id:"txt_name_department" %>
                            </div>

                            <div class="form-group" id="scode_department">
                              <label> <%= lib_translate("Department_scode")  %>  <span style="color:red;" class="red">* </span></label>
                              <%= f.text_field :scode, class: 'form-control', name: 'txt_scode_department', id:"txt_scode_department" %>
                            </div>
                            <%# author: Dong 22/05/2023 
                                update email
                             %>
                            <div class="form-group" id="email_department">
                              <label> <%= lib_translate("Email")  %> </label>
                              <%= f.text_field :email, class: 'form-control', name: 'txt_email_department', id:"txt_email_department" %>
                            </div>
                            <%# end Hai code %>
                            <div class="form-group mt-2" id="status_department">
                              <label> <%= lib_translate("Status")  %>  <span style="color:red;" class="red"></span></label>
                              <input checked type="radio" id="sel_department_status_active" class="form-check-input ms-3 me-1" value="0" name="sel_status_dep" />
                              <label class="form-check-label" style="cursor:pointer" for="sel_department_status_active">ACTIVE</label>
                              <input  type="radio" id="sel_department_status_inactive" class="form-check-input ms-3 me-1" value="1" name="sel_status_dep" />
                              <label class="form-check-label " style="cursor:pointer" for="sel_department_status_inactive">INACTIVE</label>
                            </div>
                          </div>
                          <div class=" col-4 d-flex flex-column field-group-department-name"> 
                              <%# Hai code 24/12/2022 %>
                              <div class="form-group" id="name_en_department">
                                <label  > <%= lib_translate("Department_name_en")  %>  </label>
                                <%= f.text_field :name_en, class: 'form-control', name: 'txt_name_en_department',id:"txt_name_en_department" %>
                              </div>
                              <div class="form-group" id="issued_date_department">
                                <label> <%= lib_translate("Department_issued_date")  %>  </label>
                                <div id="datepicker-container" class="datepicker-container">
                                  <span style="position: relative;" class="outline-element-container"> 
                                    <input id="dt_issued_date_dep" type="text" name="dt_issued_date_dep" class="form-control datepicker openemr-datepicker input-textbox outline-element incorrect" objtype="7" name="action_element" objindex=""  aria-label="Select Date"> 
                                    <span class="correct-incorrect-icon"> </span>
                                  </span>
                                  <div id="datepicker"></div>
                                </div>
                              </div>

                            <div class=" form-group ">
                              <label> <%= lib_translate("Manager_department")%> <span class="red">* </span></label>
                                <select class="form-select selectpicker" name="txt_leader_department" id="txt_leader_department">
                                  <% @users.each do |user| %>
                                      <option value="<%= user.email %>">
                                        <%= user.last_name + " " + user.first_name %>
                                            (<%= user.email%>)
                                      </option>
                                      <%end%>
                                </select>
                            </div>
                            <div class="form-group" id="name_en_department">
                                <label  > <%= lib_translate("Sid_department")  %>  </label>
                                <%= f.text_field :faculty, class: 'form-control', name: 'txt_faculty',id:"txt_faculty" %>
                              </div>
                          </div>
                      </div>
                      <div class="form-group" id="note_department">
                        <label> <%= lib_translate("Department_note")  %>  </label>
                        <%= f.text_area :note, class: 'form-control', name: 'department_note', id:"department_note"%>
                      </div>
                          <div class="form-group d-flex justify-content-end mt-3 mb-3">
                          <%= f.submit "" , type:"button" , id:"btn_add_new_department", class: " d-none btn btn-primary  mt-3 border-red border-pink-600 cls-bmtu-text-red md:group-hover:bg-pink-50 btn-block btn-block text-uppercase ", style: "width: 100%;"%>
                      </div>

                    <% end %>
                    <%# click lable  %>
                    <label onclick="clickCollapseDepartment(this)" id= "btn-colapse-department-label" class= "card p-2 mt-3 d-flex ps-3 pe-3" style= "width: 100%; background: var(--falcon-badge-soft-dark-background-color); flex-direction: row; align-items: center; justify-content: space-between;" type="button" data-bs-toggle="collapse" data-bs-target="#form-list-file-department" aria-expanded="false" aria-controls="form-list-file-department">
                      <%=lib_translate("List_document_file_department")%>
                      <i id="colapse-icon-department" style="transition: all 100ms ease 0s; font-size: 20px;" class="fas fa-angle-down"></i>
                    </label>
                    <%# Upload file view  %>
                      <div class="collapse " id="add-file-medias_department">
                          <div class="card hide_media" id="media_file_u">
                            <div class="card-header">
                              <h5 class="mb-0"><%=lib_translate("Upload_files")%></h5>
                            </div>
                            <div class="card-body bg-light" id="upload_file_department"></div>
                          </div>
                      </div>

                  </div>
                </div>
                <%# label = btn submit form  %>
                <label id="name-btn-department" for="btn_add_new_department" class= "btn btn-primary mt-3  border-red border-pink-600 cls-bmtu-text-red md:group-hover:bg-pink-50 btn-block text-uppercase" style= "width: 100%;">
                </label>
                <div id="loading_button_department" class= "btn btn-primary mt-3 mb-5 border-red border-pink-600 cls-bmtu-text-red md:group-hover:bg-pink-50 btn-block text-uppercase" style="opacity: 0.8;width: 100%; display:none;" >
                    <i class ="fa fa-spinner fa-spin"> </i>  <%=lib_translate("Save")%>
                </div>
            </div>
          </div>
        </div>
      <% end %>
      <hr style="width: 100%; height: 4px; background:  var(--falcon-badge-soft-secondary-background-color); ">
      <div class="table-responsive scrollbar">
        <%= render_search_pagination(departments_index_path()).html_safe %>
        <table  id="table_department" class="table table-bordered fs--1 mb-0 table-hover" style="border-collapse: collapse;">
                  <thead class="bg-200 text-900">
            <tr >
                <th class="sort" style="text-align: center;width:7%">STT</th>
                <th class="sort" data-sort="Name_department" style="text-align: center; width:240px"><%= lib_translate("Department_name")  %> </th>
                <th class="sort" data-sort="Scode_department" style="text-align: center;"><%= lib_translate("Department_scode")  %> </th>
                <th class="sort" data-sort="Email_department" style="text-align: center;"><%= lib_translate("Email")  %> </th>
                <th class="sort" data-sort="Issued_date_department" style="text-align: center;white-space: nowrap; "><%= lib_translate("Department_issued_date")  %> </th>
                <th class="sort" data-sort="Issued_by_department" style="text-align: center;"><%= lib_translate("Unit_leader")  %> </th>
                <th class="sort" data-sort="Decide_to_establish" style="text-align: center;"><%= lib_translate("Decide_to_establish")  %> </th>
                <th class="sort" data-sort="Host_unit" style="text-align: center;"><%= lib_translate("Host_unit")  %> </th>
                <th class="sort" data-sort="status_department" style="text-align: center;"><%= lib_translate("Status")  %> </th>
                <% if (is_access(session["user_id"], "DEPARTMENTS","EDIT") || is_access(session["user_id"], "DEPARTMENTS","DEL")) %> 
                <th class="no-sort" style="text-align: center;">  </th>
                <% end %>
            </tr>
          </thead>
          <tbody class="list">
        

            <% @departments.each_with_index do |department,index|%>
                <tr >
                  <td valign="middle" style="text-align: center;"><%= index+1%></td>
                  <td  class="Name_department"                     
                  <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %> 
                    onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                  <% end %>>
                    <div style="text-align: start; cursor:pointer; display: flex;flex-direction: column;">
                      <label style=" cursor:pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" class="m-0" for="<%= department.id %>"><%= department.name %></label>
                      <label style=" cursor:pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 11px; font-style: italic; color: var(--falcon-badge-soft-secondary-color);" class="m-0" for="<%= department.id %>"><%= department.name_en %></label>
                      <input type="checkbox" name="<%= department.id %>" id="<%= department.id %>" data-toggle="toggle">
                    </div>
                  </td>
                  <td valign="middle" style="text-align: center;cursor: pointer;" class="Scode_department"
                  <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %> 
                    onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                  <% end %>>
                    <%= department.scode %>
                  </td>

                  <td valign="middle" style="text-align: center;cursor: pointer;" class="Email_department"
                  <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %> 
                    onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                  <% end %>>
                    <%= department.email %>
                  </td>

                  <td valign="middle" style="text-align: center;cursor: pointer;" class="Issued_date_department" 
                  <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %>                 
                    onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                  <% end %>>
                    <%= department.issued_date&.strftime('%d/%m/%Y') %>
                  </td>

                  <td valign="middle" style="text-align: start;cursor: pointer;" class="Issued_by_department" 
                  <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %>                 
                    onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                  <% end %>>
                    <% User.where(email: department.leader).each do |user| %>      
                      <div style="text-align: start; cursor:pointer; display: flex;flex-direction: column;">
                        <label style=" cursor:pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" class="m-0"><%= user.last_name + " " + user.first_name %></label>
                        <label style=" cursor:pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 11px; font-style: italic; color: var(--falcon-badge-soft-secondary-color);" class="m-0" >(<%= user.email%>)</label>
                      </div>                     
                    <%end%>
                  </td>

                  <td valign="middle" style="text-align: start;cursor: pointer;" class="Decide_to_establish" 
                  <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %>                 
                    onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                  <% end %>>
                    <%=  department.issue_id  %>
                  </td>


                  <td valign="middle" style="text-align: center;cursor: pointer; " class="Host_unit" 
                  <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %>                 
                    onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                  <% end %>>
                    <% oOrg =  Organization.where(id: department.organization_id).first
                      if !oOrg.nil? 
                    %>
                          <%= oOrg.scode %>
                      <%end%>
                  </td>

                  <td valign="middle" style="text-align: center;cursor: pointer;white-space: nowrap;" class="status_department" 
                  <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %>                 
                    onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                  <% end %>>
                    <%= (
                    if department.status == "0"
                        lib_translate("ac_icon")
                    else department.status == "1"
                        lib_translate("inac_icon")
                    end
                    ) %>
                  </td>
                  <% if (is_access(session["user_id"], "DEPARTMENTS","EDIT") || is_access(session["user_id"], "DEPARTMENTS","DEL")) %> 
                    <td valign="middle" style="text-align: center; white-space: nowrap;"  >
                    <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %> 
                        <a data-toggle="tooltip" data-placement="top" title="Chi tiết đơn vị"  style="cursor:pointer; color: #2192FF;text-decoration: none;"   
                          <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %>                       
                            onclick="window.location='<%= department_details_path(id: department.id, lang: session[:lang]) %>';"
                          <% end %>>
                              <i class="far fa-edit icon"></i>
                        </a>
                    <% end %>
                    <% if is_access(session["user_id"], "DEPARTMENTS","DEL") %> 
                      <a data-toggle="modal" data-placement="top" title="Xóa đơn vị" data-toggle="modal" data-target="#exampleModal-#{<%= department.id %>}" style="cursor: pointer;text-decoration: none;">
                            <i class="fas fa-trash text-danger icon ms-2" style="width : 15px"></i>
                      </a>
                      <div class="modal fade" id="exampleModal-#{<%= department.id %>}" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                          <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                  <div class="modal-header">
                                  <h5 class="modal-title" id="exampleModalLabel"><%= lib_translate("Are_you_sure")  %></h5>
                                  <button style="border: none;background: white;" type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span style="border: none;background: white;color: red;font-weight: bold;font-size: 20px;" aria-hidden="true">&times;</span>
                                  </button>
                                  </div>
                                <div class="modal-body">
                                  <%= lib_translate("Are_you_sure_to_delete_this_department")  %>  <span class="fw-bold"  style="font-weight: bold; color: red"> <%= department.scode %></span>?
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" id="close_department_delete" data-dismiss="modal"><%= lib_translate("Close")  %></button>
                                      <%= link_to lib_translate("Delete"), url_for(action: :del, id: department.id, ck_acction: "department"), method: :delete, class: "btn btn-danger", id:"delete_department_delete_form" %>
                                        <div id="loading_button_delete_department" class= "btn btn-danger" style="opacity: 0.8; width: 100%; display:none;" >
                                            <i class ="fa fa-spinner fa-spin"> </i>  <%=lib_translate("Deleting")%>
                                        </div>
                                </div>
                            </div>
                          </div>
                      </div>
                    <% end %>
                    <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %> 
                      <a data-toggle="tooltip" data-placement="top" title="Xem tài liệu đơn vị" data-bs-toggle="collapse"  onclick="clickEditMediaInTable(<%= department.id%>)" href="#collapse_file_document-<%= department.id  %>"
                        role="button" data-target="collapse_file_document-<%= department.id  %>" aria-expanded="false" >
                        <span class=" text-warning fas fa-folder-open icon ms-2"></span>
                      </a>
                    <% end %>

                    </td>
                  <% end %>
                </tr>
              <% if is_access(session["user_id"], "DEPARTMENTS","EDIT") %> 
                <tr style="background: var(--falcon-border-color);" class="collapse" id="collapse_file_document-<%= department.id  %>" >
                  <td style="display: none"><%= index+1%></td>
                  <td style="display: none" class="Name_department" >
                    <div style="text-align: start; cursor:pointer; display: flex;flex-direction: column;">
                      <label style=" cursor:pointer" class="m-0" for="<%= department.id %>"><%= department.name %></label>
                      <label style=" cursor:pointer; font-size: 11px; font-style: italic; color: var(--falcon-badge-soft-secondary-color);" class="m-0" for="<%= department.id %>"><%= department.name_en %></label>
                      <input type="checkbox" name="<%= department.id %>" id="<%= department.id %>" data-toggle="toggle">
                    </div>
                  </td>
                  <td style="display: none" class="Scode_department">
                    <%= department.scode %>
                  </td>

                  <td style="display: none" class="Email_department">
                    <%= department.email %>
                  </td>

                  <td style="display: none" class="Issued_date_department">
                    <%= department.issued_date&.strftime('%d/%m/%Y') %>
                  </td>
                  <td  style="display: none" class="Issued_by_department">
                     <% User.where(email: department.leader).each do |user| %>
                          <%= (
                            if user.gender == "0"
                                lib_translate("Mr") +" "+ user.first_name.capitalize.titleize
                            else user.gender == "1"
                              lib_translate("Mrs") +" "+ user.first_name.capitalize.titleize
                            end) 
                          %>
                      <%end%>
                  </td>
                  <td style="display: none" class="Decide_to_establish">
                    <%=  department.issue_id  %>
                  </td>
                  <td style="display: none " class="Host_unit">
                    <% oOrg =  Organization.where(id: department.organization_id).first
                      if !oOrg.nil? 
                    %>
                          <%= oOrg.scode %>
                      <%end%>
                  </td>
                  <td style="display: none" class="status_department">
                    <%= (
                    if department.status == "0"
                        lib_translate("ac_icon")
                    else department.status == "1"
                        lib_translate("inac_icon")
                    end
                    ) %>
                  </td>
                  <td colspan="10" style="padding: 0;" class="line_upload_department">
                      <div id="department-table-media-<%= department.id %>" style="margin: 10px;padding: 10px; border-radius: 10px;" class="capp-form-bg"></div>
                  </td>
                </tr>
              <% end %>
            <% end %>
          </tbody>
        </table>
        <%= render_pagination_limit_offset(departments_index_path(lang: session[:lang], per_page: session[:per_page], search: session[:search]), 10, @departments.count).html_safe %>
      </div>
    </div>
  </div>

</div>
<script>
  $(document).on('change', '#organization_id', function () {
    const orgId = $(this).val();

    if (orgId === "") {
      $('#department_parent').empty();
      $('#department_parent').append('<option value="">Chọn đơn vị cha</option>');
      return;
    }

    $.ajax({
      url: '<%= departments_fetch_by_organization_path%>',
      method: "GET",
      data: { organization_id: orgId },
      dataType: "script"
    });
  });

  $('#delete_department_delete_form').click(function () {
    $('#delete_department_delete_form').hide();
    $('#close_department_delete').hide();
    $('#loading_button_delete_department').show();
  });
  let Choose_date = "<%= lib_translate("Choose_date") %>";
  let closeText = "<%= lib_translate("closeText") %>";
  let prevText = "<%= lib_translate("prevText") %>";
  let nextText = "<%= lib_translate("nextText") %>";
  let currentText = "<%= lib_translate("currentText") %>";
  let Jan = "<%= lib_translate("Jan") %>";
  let Feb = "<%= lib_translate("Feb") %>";
  let Mar = "<%= lib_translate("Mar") %>";
  let Apr = "<%= lib_translate("Apr") %>";
  let May = "<%= lib_translate("May") %>";
  let Jun = "<%= lib_translate("Jun") %>";
  let Jul = "<%= lib_translate("Jul") %>";
  let Aug = "<%= lib_translate("Aug") %>";
  let Sep = "<%= lib_translate("Sep") %>";
  let Oct = "<%= lib_translate("Oct") %>";
  let Nov = "<%= lib_translate("Nov") %>";
  let Dec = "<%= lib_translate("Dec") %>";
  let Mon = "<%= lib_translate("Mon") %>";
  let Tue = "<%= lib_translate("Tue") %>";
  let Wed = "<%= lib_translate("Wed") %>";
  let Thu = "<%= lib_translate("Thu") %>";
  let Fri = "<%= lib_translate("Fri") %>";
  let Sat = "<%= lib_translate("Sat") %>";
  let Sun = "<%= lib_translate("Sun") %>";


  // datepicker
  $( "#dt_issued_date_dep" ).datepicker({
    showButtonPanel: true,
    dateFormat: "dd/mm/yy",
    changeMonth: true,
    changeYear: true,
    yearRange: "c-100:c+10",
    dayNamesMin : [ "S", "M", "T", "W", "T", "F", "S" ],
    // defaultDate: +1,
    buttonImageOnly: true,
    buttonImage: "<%= image_path('icon-calender-datepicker.png') %>",
    showOn: "button",
    buttonText: Choose_date,
    closeText: closeText,
    prevText: prevText,
    nextText: nextText,
    currentText: currentText,
    monthNamesShort: [Jan, Feb, Mar, Apr,
    May, Jun, Jul, Aug,
    Sep, Oct, Nov, Dec],
    dayNamesMin: [Mon, Tue, Wed, Thu,
    Fri, Sat, Sun],
    firstDay: 1,
    isRTL: false,
    showMonthAfterYear: false,
    yearSuffix: "",
  });
  $(function () {
        $(".datepicker").on('keydown', function (e) {
            IsNumeric(this, e.keyCode);
        });
        var isShift = false;
        var seperator = "/";
        function IsNumeric(input, keyCode) {
            if (keyCode == 16) {
                isShift = true;
            }
            //Allow only Numeric Keys.
            if (((keyCode >= 48 && keyCode <= 57) || keyCode == 8 || keyCode <= 37 || keyCode <= 39 || (keyCode >= 96 && keyCode <= 105)) && isShift == false) {
                if ((input.value.length == 2 || input.value.length == 5) && keyCode != 8) {
                    input.value += seperator;
                }
                return true;
            }
            else {
                return false;
            }
        };
        $(".datepicker").keyup(function(e) {
          var datecheck = /^(?:(?:31(\/|-|\.)(?:0?[13578]|1[02]|(?:Jan|Mar|May|Jul|Aug|Oct|Dec)))\1|(?:(?:29|30)(\/|-|\.)(?:0?[1,3-9]|1[0-2]|(?:Jan|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec))\2))(?:(?:1[6-9]|[2-9]\d)?\d{2})$|^(?:29(\/|-|\.)(?:0?2|(?:Feb))\3(?:(?:(?:1[6-9]|[2-9]\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\d|2[0-8])(\/|-|\.)(?:(?:0?[1-9]|(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep))|(?:1[0-2]|(?:Oct|Nov|Dec)))\4(?:(?:1[6-9]|[2-9]\d)?\d{2})$/g;
          var textcheck = /[A-Za-z]/g;
          var special = /[!"`'#%&.,:;<>=@{}~\$\(\)\*\+\-\\\?\[\]\^\|]+/;
          var unikey = /^[a-zA-Z_ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+$/;
             if (!datecheck.test(this.value))
              {
                this.value = this.value.replace(textcheck, '');
                this.value = this.value.replace(special, '');
                this.value = this.value.replace(unikey, '');
              }

              else {

              }

        });

  });
  let Add_new_Department = "<%= lib_translate("Add_new_Department") %>";
  let Update_Department = "<%= lib_translate("Update_Department") %>";
  let Please_enter_department_name = "<%= lib_translate("Please_enter_department_name") %>";
  let Please_enter_department_scode = "<%= lib_translate("Please_enter_department_scode") %>";
  let Please_enter_department_issued_id = "<%= lib_translate("Please_enter_department_issued_id") %>";
  function generate_scode_department(old_scode,org_scode) {
    <%
      scodes = []
      @organizations.each do |organization|
        scodes.push(organization.scode)
      end
    %>
    let list_scodes = <%= scodes.to_json.html_safe%>;
    var lastId = "<%= !Department.last.nil? ?  Department.last.id : ""%>";
    let default_scode = "BMTU";
    if(!old_scode || old_scode.trim().length== ""){
      if (org_scode){
        return org_scode + lastId;
      }else{
        return default_scode + lastId;
      }
    }else{
      for (let i = 0; i < list_scodes.length; i++) {
        const scode = list_scodes[i];
        if(old_scode.includes(scode)){
          old_scode = old_scode.replace(scode,org_scode);
          break;
        }
      }
    }
    return old_scode;
  }

  function openFormAddDepartment() {
    $('#id_cancel_department').css({"display" : "block"});
    $('#btn_add_department').css({"display" : "none"});
    $("#btn-colapse-department-label").toggleClass("d-none",true);
    $("#cls_bmtu_form_add_title").html(Add_new_Department);
    $("#name-btn-department").html(Add_new_Department);
    $('#form_add_department').trigger("reset");
    $("#dt_issued_date_dep").val('<%= Time.now.strftime("%d/%m/%Y")%>');
    $("#txt_scode_department").val(generate_scode_department(null,'<%= @user_organization_scode%>'));
  }
  var media_trans = {
    upload_guide_1:'<%= lib_translate("upload_guide_1") %>',
    upload_guide_2:'<%= lib_translate("upload_guide_2") %>',
    upload_guide_3:'<%= lib_translate("upload_guide_3") %>',
    confirm_delete:'<%= lib_translate("confirm_delete") %>',
    confirm_delete_all:'<%= lib_translate("confirm_delete_all") %>',
    select_files:'<%= lib_translate("select_files") %>',
    cancel:'<%= lib_translate("Cancel") %>',
    confirm:'<%= lib_translate("Confirm") %>',
    message:'<%= lib_translate("Message") %>',
    remove:'<%= lib_translate("Remove") %>',
    upload:'<%= lib_translate("Upload") %>',
    error:'<%= lib_translate("error") %>',
    try_again:'<%= lib_translate("try_again") %>',
    file_name:'<%= lib_translate("File_name") %>',
    create_date:'<%= lib_translate("Create_date") %>',
    created_by:'<%= lib_translate("Created_by") %>'
  }
  const formMediaDepartment = new FormMedia("upload_file_department");
  formMediaDepartment.setIconPath('<%= root_path%>assets/image/');
  formMediaDepartment.setTranslate(media_trans);
  formMediaDepartment.init();
  formMediaDepartment.addEventListener("confirmdel",(data)=>{
    deleteDoc(data.id,data.department_id);
  });
  $('#id_cancel_department').click(function(){
    $('#id_cancel_department').css({"display" : "none"});
    $('#btn_add_department').css({"display" : "block"});
  });
  
  function clickCollapseDepartment(element){
    $('#add-file-medias_department').collapse("show");
    var media_open = document.getElementById("media_file_u");
    var media_file_deparment = media_open.getAttribute("class");
    let width = window.innerWidth;
    if  (media_file_deparment.includes("hide_media")) {
      media_open.classList.add("show_media");
      media_open.classList.remove("hide_media");
      } else {
      media_open.classList.remove("show_media");
      media_open.classList.add("hide_media");
    }
  }

  var error_label= document.getElementById('erro_labble_content');
  // HVu Organization
  $("#organization_id").on("change", function(e) {
    let department_id =  $("#department_id").val();
    
    if(department_id && department_id.trim() != ""){
        return;
    }

    let scode_select = $('#organization_id option:selected').attr("data-scode");

    var old_scode = $("#txt_scode_department").val();
    let newDepartmentScode = generate_scode_department(old_scode,scode_select);
    
    $("#txt_scode_department").val(newDepartmentScode);
  });

  
  // HVu: Organization end

  function clickEditMediaInTable(department_id){
      if($(`#department-table-media-${department_id}`).html().length == 0){
        let action_upload = '<%= url_for(action: :departments_upload_mediafile)%>';
        var formmedia_edit_doc = new FormMedia("department-table-media-" + department_id);
        formmedia_edit_doc.setAction(action_upload + "?department_id="+ department_id);
        formmedia_edit_doc.setIconPath('<%= root_path%>assets/image/');
        formmedia_edit_doc.setTranslate(media_trans);
        formmedia_edit_doc.init();
        formmedia_edit_doc.addEventListener("confirmdel",(data)=>{
          deleteDoc(data.id,data.department_id);
        });

        $.ajax({
            type: "POST",
            url: "<%= department_update_path%>",
            data: { idDepartment: department_id },
            dataType: "JSON",
            success: function (response) {
              let docs = response.listDe;
              formmedia_edit_doc.tableAddItems(docs);
            }
        });
      }else{

      }

  }
  function deleteDoc(doc_id,id){
    let action = `<%= department_del_path%>?id=${id}&did=${doc_id}&ck_action=document&currentpage=index` ;
    doClick(action,'delete');
  }

  document.getElementById("txt_name_department").addEventListener("change", function() {
      var name = document.getElementById('txt_name_department').value;
      error_label.style.display="none";
      var name_erro = document.getElementById('txt_name_department');
      name_erro.style.border= "1px solid #ced4da";
  });
  document.getElementById("txt_scode_department").addEventListener("change", function() {
      var scode_erro = document.getElementById('txt_scode_department');
      error_label.style.display="none";
      scode_erro.style.border= "1px solid #ced4da";
  });

  $(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip({
      delay: { "show": 100, "hide": 100 }
    });
  });
</script>


